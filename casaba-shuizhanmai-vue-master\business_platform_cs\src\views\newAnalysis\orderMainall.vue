<template>
    <div class="padding-bottom-30">
        <!-- 超时统计 begin ----------- -->
        <div class="timeout-stats-container" style="margin-bottom: 15px;">
            <!-- 超时未配送统计 -->
            <div class="stats-section" style="margin-bottom: 10px;">
                <div class="stats-title" style="margin-bottom: 8px; font-size: 14px; font-weight: bold; color: #333;">
                    超时未配送统计
                </div>
                <div class="stats-cards" style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <div
                        v-for="(stat, index) in timeoutStats"
                        :key="'undelivered-' + index"
                        class="stat-card"
                        :class="{ 'active': selectedTimeoutFilter === stat.key && timeoutType === 'undelivered' }"
                        @click="filterByTimeout(stat.key, 'undelivered')"
                        style="
                            flex: 1;
                            min-width: 120px;
                            padding: 8px 12px;
                            background: #fff;
                            border: 1px solid #e6e6e6;
                            border-radius: 6px;
                            text-align: center;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        "
                    >
                        <div class="stat-label" style="font-size: 11px; color: #666; margin-bottom: 2px;">
                            {{ stat.label }}
                        </div>
                        <div class="stat-count" style="font-size: 18px; font-weight: bold; color: #409EFF;">
                            {{ stat.count }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 超时未送达统计 -->
            <div class="stats-section">
                <div class="stats-title" style="margin-bottom: 8px; font-size: 14px; font-weight: bold; color: #333;">
                    超时未送达统计
                </div>
                <div class="stats-cards" style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <div
                        v-for="(stat, index) in undeliveredTimeoutStats"
                        :key="'undelivered-timeout-' + index"
                        class="stat-card undelivered-stat-card"
                        :class="{ 'active': selectedTimeoutFilter === stat.key && timeoutType === 'undelivered_timeout' }"
                        @click="filterByTimeout(stat.key, 'undelivered_timeout')"
                        style="
                            flex: 1;
                            min-width: 120px;
                            padding: 8px 12px;
                            background: #fff;
                            border: 1px solid #e6e6e6;
                            border-radius: 6px;
                            text-align: center;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        "
                    >
                        <div class="stat-label" style="font-size: 11px; color: #666; margin-bottom: 2px;">
                            {{ stat.label }}
                        </div>
                        <div class="stat-count" style="font-size: 18px; font-weight: bold; color: #f56c6c;">
                            {{ stat.count }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 水站详情展示 begin ----------- -->
        <div v-if="showStoreDetails" class="store-details-container" style="margin-bottom: 15px;">
            <div class="details-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <div style="font-size: 16px; font-weight: bold; color: #333;">
                    {{ timeoutType === 'undelivered' ? '超时未配送' : '超时未送达' }}水站详情
                    <span style="font-size: 12px; color: #666; margin-left: 10px;">
                        ({{ selectedTimeoutFilter === '1h' ? '1小时内' :
                             selectedTimeoutFilter === '2h' ? '1-2小时' :
                             selectedTimeoutFilter === '4h' ? '2-4小时' :
                             selectedTimeoutFilter === '8h' ? '4-8小时' :
                             selectedTimeoutFilter === '16h' ? '8-16小时' :
                             selectedTimeoutFilter === '24h' ? '16-24小时' :
                             selectedTimeoutFilter === '48h' ? '24-48小时' :
                             selectedTimeoutFilter === '72h' ? '48-72小时' :
                             selectedTimeoutFilter === '72h+' ? '72小时以上' : '' }})
                    </span>
                </div>
                <el-button size="small" @click="backToStats">返回统计</el-button>
            </div>

            <div class="store-list" v-loading="detailsLoading">
                <div
                    v-for="store in storeTimeoutStats"
                    :key="store.storeId"
                    class="store-item"
                    @click="viewStoreOrders(store)"
                    style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 12px 16px;
                        margin-bottom: 8px;
                        background: #fff;
                        border: 1px solid #e6e6e6;
                        border-radius: 6px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    "
                    :style="{
                        'border-color': selectedStoreId === store.storeId ? '#409EFF' : '#e6e6e6',
                        'background': selectedStoreId === store.storeId ? '#f0f9ff' : '#fff'
                    }"
                >
                    <div class="store-info">
                        <div class="store-name" style="font-size: 14px; font-weight: bold; color: #333; margin-bottom: 4px;">
                            {{ store.storeName }}
                        </div>
                        <div class="store-contact" style="font-size: 12px; color: #666;">
                            {{ store.storePhone || '暂无联系方式' }}
                        </div>
                    </div>
                    <div class="store-count" style="text-align: right;">
                        <div class="count-number" style="font-size: 18px; font-weight: bold; color: #f56c6c;">
                            {{ store.timeoutCount }}
                        </div>
                        <div class="count-label" style="font-size: 11px; color: #666;">
                            超时订单
                        </div>
                    </div>
                </div>

                <div v-if="storeTimeoutStats.length === 0 && !detailsLoading" style="text-align: center; padding: 40px; color: #999;">
                    暂无超时订单数据
                </div>
            </div>
        </div>

        <!-- 当前筛选状态提示 -->
        <div v-if="selectedTimeoutFilter && !selectedStoreId" class="current-filter-tip" style="margin-bottom: 15px; padding: 10px; background: #fff7e6; border: 1px solid #ffa940; border-radius: 6px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="color: #fa8c16; font-size: 14px;">
                    <i class="el-icon-info"></i>
                    当前显示：所有{{ timeoutType === 'undelivered' ? '超时未配送' : '超时未送达' }}订单
                    <span style="margin-left: 10px; font-size: 12px; color: #666;">
                        ({{ selectedTimeoutFilter === '1h' ? '1小时内' :
                             selectedTimeoutFilter === '2h' ? '1-2小时' :
                             selectedTimeoutFilter === '4h' ? '2-4小时' :
                             selectedTimeoutFilter === '8h' ? '4-8小时' :
                             selectedTimeoutFilter === '16h' ? '8-16小时' :
                             selectedTimeoutFilter === '24h' ? '16-24小时' :
                             selectedTimeoutFilter === '48h' ? '24-48小时' :
                             selectedTimeoutFilter === '72h' ? '48-72小时' :
                             selectedTimeoutFilter === '72h+' ? '72小时以上' : '' }})
                    </span>
                </div>
                <div>
                    <el-button size="small" @click="backToStats">返回统计</el-button>
                </div>
            </div>
        </div>

        <div v-if="selectedStoreId && selectedStoreName" class="current-filter-tip" style="margin-bottom: 15px; padding: 10px; background: #f0f9ff; border: 1px solid #409EFF; border-radius: 6px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="color: #409EFF; font-size: 14px;">
                    <i class="el-icon-info"></i>
                    当前显示：{{ selectedStoreName }} - {{ timeoutType === 'undelivered' ? '超时未配送' : '超时未送达' }}订单
                    <span style="margin-left: 10px; font-size: 12px; color: #666;">
                        ({{ selectedTimeoutFilter === '1h' ? '1小时内' :
                             selectedTimeoutFilter === '2h' ? '1-2小时' :
                             selectedTimeoutFilter === '4h' ? '2-4小时' :
                             selectedTimeoutFilter === '8h' ? '4-8小时' :
                             selectedTimeoutFilter === '16h' ? '8-16小时' :
                             selectedTimeoutFilter === '24h' ? '16-24小时' :
                             selectedTimeoutFilter === '48h' ? '24-48小时' :
                             selectedTimeoutFilter === '72h' ? '48-72小时' :
                             selectedTimeoutFilter === '72h+' ? '72小时以上' : '' }})
                    </span>
                </div>
                <div>
                    <el-button size="small" @click="backToStoreList">返回水站列表</el-button>
                    <el-button size="small" @click="backToStats">返回统计</el-button>
                </div>
            </div>
        </div>

        <!-- 送水员提成管理 begin ----------- -->
        <div>
            <div class="content-box"></div>
            <div class="cont-cent">
                <el-form :inline="true">
                    <el-form-item>
                        <el-input placeholder="输入订单号/产品/手机号/联系方式/地址搜索" v-model="username" clearable
                            style="width:250px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input placeholder="输入备注" v-model="userContent" clearable style="width:250px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-cascader
                            v-model="areaFilter"
                            placeholder="全部(区域)"
                            :options="areaOptions"
                            :props="cascaderProps"
                            filterable
                            clearable
                            style="width: 200px;"
                            @change="onAreaFilterChange">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="storeId" filterable clearable>
                            <el-option label="全部(商家)" value=""></el-option>
                            <el-option v-for="item in filteredStoreList" :key="item.storeId" :label="item.storeAme"
                                :value="item.storeId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="ordersource" filterable clearable>
                            <el-option label="全部(订单来源)" value=""></el-option>
                            <el-option v-for="item in ordersourcefilter" :key="item.key" :label="item.value"
                                :value="item.key" :disabled="item.disabled" :show="item.isShow"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="appkey" filterable clearable>
                            <el-option v-for="item in appkeylist" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker @change="selectDataDate" v-model="dateTime" type="daterange" align="right"
                            unlink-panels range-separator="至" start-placeholder="开始日期(创建时间)"
                            end-placeholder="结束日期(创建时间)" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker @change="selectDataDateFinish" v-model="dateTimeFinish" type="daterange"
                            align="right" unlink-panels range-separator="至" start-placeholder="开始日期(送达时间)"
                            end-placeholder="结束日期(送达时间)" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                    <el-select v-model="selectVal" placeholder="按订单状态筛选" clearable >
                        <el-option v-for="(item, index) in selectOptions" :key="index" :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select></el-form-item>
                    <el-form-item>
                        <el-select v-model="backFilter" placeholder="退款订单筛选" clearable>
                            <el-option label="全部订单" value=""></el-option>
                            <el-option label="退单订单" value="1"></el-option>
                            <el-option label="正常订单" value="0"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item style="margin-left:20px;">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button @click="clearSearch">清空筛选条件</el-button>
                        <el-button v-if="selectedTimeoutFilter" type="warning" size="small" @click="clearTimeoutFilter">
                            清除{{ timeoutType === 'undelivered' ? '未配送' : '未送达' }}超时筛选
                        </el-button>
                    </el-form-item>
                    <el-button type="success" icon="el-icon-download" size="mini" @click="exportHandle">导出数据</el-button>
                    <!-- <el-button type="primary">
                        <Upload @uploaded="getszmcordermain" :url="'/pdd/importExcel'" :name="'拼多多导入'"></Upload>
                    </el-button> -->
                    <!-- <el-button type="success" @click="pinduoduorefund">拼多多退款</el-button> -->
                    <!-- <el-button type="success" @click="pinduoduotest">拼多多测试</el-button> -->
                </el-form>
            </div>

            <!-- 批量操作按钮 -->
            <div style="margin: 10px 0;" v-if="selectedOrders.length > 0">
                <el-button type="warning" size="small" @click="batchUpdateMark">
                    批量设置标记 ({{ selectedOrders.length }})
                </el-button>
                <el-button type="success" size="small" @click="batchUpdateStoreByGeofence">
                    批量围栏更换商家 ({{ selectedOrders.length }})
                </el-button>
                <el-button size="small" @click="clearSelection">取消选择</el-button>
            </div>

            <div>
                <!-- 历史订单表 -->
                <el-table ref="orderTable" :data="szmcorderlist" key="pledBucket7" :header-cell-style="{
                    'text-align': 'center',
                    'background-color': '#EFF2F7',
                }" :cell-style="{
                    'text-align': 'center',
                    'font-size': '13px',
                    color: '#333C48',
                }" stripe border v-loading="fuckLoading10" element-loading-text="拼命加载中" class="productList"
                    style="width: 100%;" @selection-change="handleSelectionChange">
                    <!-- 多选列 -->
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="mobile" label="订单来源">
                        <div slot-scope="scope">
                            <img v-if="scope.row.orderSourceImage" style="width: 40px;height: 40px;"
                                :src="scope.row.orderSourceImage"
                                alt="">

                        </div>
                    </el-table-column>
                    <el-table-column prop="mobile" label="所属商家">
                        <div @click="updatestore(scope.row.orderId)" slot-scope="scope">
                            <div>{{ scope.row.storeName || '-' }}</div>
                            <div v-if="scope.row.storeMobile">{{ scope.row.storeMobile || '-' }}</div>
                            <div v-if="scope.row.storeProvinceName">
                                {{ scope.row.storeProvinceName }}
                                {{ scope.row.storeCityName ? ('-' + scope.row.storeCityName) : '' }}
                                {{ scope.row.storeAreaName ? ('-' + scope.row.storeAreaName) : '' }}
                            </div>
                        </div>
                    </el-table-column>
                    <el-table-column prop="mobile" label="登录手机号"></el-table-column>
                    <el-table-column prop="userName" label="联系人"></el-table-column>
                    <el-table-column prop="userPhone" label="联系方式">
                        <template slot-scope="scope">
                                <div>{{ scope.row.userPhone }}</div>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    style="background-color: #1895fd; border: none; padding: 4px 8px;"
                                    @click="refreshPhone(scope.row.orderId)"
                                    :loading="scope.row.refreshing"
                                >
                                    刷新电话
                                </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column prop="userAddress" label="下单地址"></el-table-column>
                    <el-table-column prop="orderDate" label="下单日期"></el-table-column>
                    <el-table-column prop="finishTime" label="送达日期"></el-table-column>
                    <el-table-column prop="orderNumber" label="订单编码" width="150">
                        <template slot-scope="scope">
                            <div>{{ scope.row.orderNumber }}</div>
                            <div @click="showOrderLog(scope.row.orderNumber)" class="order-log-btn">
                                操作记录
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单信息" min-width="150">
                        <template slot-scope="scope">
                            <div v-if="scope.row.groupShopList">
                                <div v-for="(item, index) in scope.row.groupShopList" :key="index">
                                    {{ item.groupName }} x {{ item.groupNumber }}
                                </div>
                            </div>
                            <div v-if="scope.row.list && scope.row.list.length > 0">
                                <div v-for="(item, index) in scope.row.list[0].orderShopDeatilList" :key="index">
                                    {{ item.title }} x {{ item.shopNumber }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderTotalNumber" label="总件数"></el-table-column>
                    <el-table-column prop="orderPrice" label="订单总金额">
                        <template slot-scope="scope">￥{{ scope.row.orderPrice }}</template>
                    </el-table-column>
                    <el-table-column prop="deliveryName" label="送水员">
                        <template slot-scope="scope">
                            <div>{{ scope.row.deliveryName }}</div>
                            <div v-if="scope.row.deliveryMobile">{{ scope.row.deliveryMobile }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="userContent" label="备注"></el-table-column>
                    <el-table-column v-if="selectedTimeoutFilter" prop="timeoutHours" label="超时时长" width="100">
                        <template slot-scope="scope">
                            <span style="color: #f56c6c; font-weight: bold;">
                                {{ formatTimeoutHours(scope.row.timeoutHours) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="newOrderState" label="状态" width="100">
                        <template slot-scope="scope">
                            <span>{{ scope.row.newOrderState }}</span>
                            <span v-if="scope.row.orderReturnStete">({{ scope.row.orderReturnStete }})</span>

                        </template>
                    </el-table-column>
                    <el-table-column prop="picurl" label="图片" width="120">
                        <template slot-scope="scope">
                            <div v-if="getPicUrlArray(scope.row.picurl).length > 0" class="pic-preview-btn-container">
                                <el-button
                                    type="text"
                                    size="mini"
                                    icon="el-icon-picture-outline"
                                    @click="previewImages(getPicUrlArray(scope.row.picurl), 0)"
                                    class="pic-preview-btn">
                                    查看图片({{ getPicUrlArray(scope.row.picurl).length }})
                                </el-button>
                            </div>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="mark" label="标记" width="100">
                        <template slot-scope="scope">
                            <span v-if="scope.row.mark"
                                  class="mark-text"
                                  @click="updateMark(scope.row.orderId, scope.row.mark)">
                                {{ scope.row.mark }}
                            </span>
                            <span v-else
                                  class="mark-placeholder"
                                  @click="updateMark(scope.row.orderId, scope.row.mark)">
                                设置标记
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderState" label="操作" width="260">
                        <template slot-scope="scope">
                            <!-- <el-button style="margin-top: 3px;" v-if="adminStoreInfo.role1 == 4" type="primary" size="mini" @click="print(scope.row)">打印</el-button> -->
                            <el-button style="margin-top: 3px;" type="primary" size="mini" 
                                v-if="(scope.row.orderState == 8 && scope.row.orderReturnStete == '申请中') 
                                || (scope.row.orderState != 6 && scope.row.orderState != 8)"
                                @click="orderrefund(scope.row.orderId)">退款</el-button>
                            <el-button style="margin-top: 3px;" type="success" size="mini" v-if="scope.row.orderState < 5"
                                @click="updatestore(scope.row.orderId)">更换商家</el-button>
                            <el-button style="margin-top: 3px;" type="info" size="mini" v-if="scope.row.orderState < 5"
                                @click="updateStoreByGeofence(scope.row.orderId)">围栏更换商家</el-button>
                            <el-button style="margin-top: 3px;" type="success" size="mini" v-if="scope.row.orderState == 8"
                                @click="updateOrderState(scope.row.orderId)">恢复派送</el-button>
                            <el-button style="margin-top: 3px;" type="warning" size="mini" @click="uploadVoucher(scope.row.orderId)">上传凭证</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="padding flex align-items-center justify-content-center">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    layout="total, prev, pager, next,sizes, jumper" background :current-page="page + 1"
                    :page-sizes="currentPageSizes" :page-size="pageSize" :total="pageTotal"></el-pagination>
            </div>
        </div>
        <ordermainupdatestore v-if="ordermainupdatestoreVisible" ref="ordermainupdatestore"
            @refreshDataList="getszmcordermain">
        </ordermainupdatestore>
        <ordermainpinduoduorefund v-if="ordermainpinduoduorefundVisible" ref="ordermainpinduoduorefund"
            @refreshDataList="getszmcordermain">
        </ordermainpinduoduorefund>
        <ordermainupdatemark v-if="ordermainupdatemarkVisible" ref="ordermainupdatemark"
            @refreshDataList="getszmcordermain">
        </ordermainupdatemark>

        <!-- 图片预览和上传凭证组件 -->
        <ImagePreviewUpload
            :visible.sync="imagePreviewVisible"
            :image-list="previewImageList"
            :initial-index="currentImageIndex"
            :upload-visible.sync="uploadVoucherVisible"
            :orderMainId="voucherForm.orderId"
            :existing-images="voucherForm.existingPics"
            @upload-success="handleVoucherSuccess"
            ref="imagePreviewUpload" />

        <!-- 批量设置标记对话框 -->
        <el-dialog title="批量设置标记" :visible.sync="batchMarkVisible" width="400px" center>
            <el-form :model="batchMarkForm" :rules="batchMarkRules" ref="batchMarkForm" label-width="80px">
                <el-form-item>
                    <div style="color: #666; font-size: 12px; margin-bottom: 15px;">
                        将为 {{ selectedOrders.length }} 个订单设置标记
                    </div>
                </el-form-item>
                <el-form-item label="选择标记" prop="mark">
                    <el-radio-group v-model="batchMarkForm.mark">
                        <el-radio label="月结">月结</el-radio>
                        <el-radio label="10个工作日">10个工作日</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchMarkVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmBatchUpdateMark" :loading="batchMarkLoading">确定</el-button>
            </div>
        </el-dialog>

        <!-- 批量围栏更换商家对话框 -->
        <el-dialog title="批量围栏更换商家" :visible.sync="batchGeofenceStoreVisible" width="500px" center>
            <el-form :model="batchGeofenceStoreForm" :rules="batchGeofenceStoreRules" ref="batchGeofenceStoreForm" label-width="100px">
                <el-form-item>
                    <div style="color: #666; font-size: 12px; margin-bottom: 15px;">
                        将根据最新围栏规则为 {{ selectedOrders.length }} 个订单自动判断并更换商家
                    </div>
                    <div style="color: #f56c6c; font-size: 11px; margin-bottom: 15px;">
                        注意：系统将根据订单地址坐标和最新围栏配置自动判断应归属的商家，只有当判断结果与当前商家不同时才会执行更换
                    </div>
                </el-form-item>
                <el-form-item label="更换原因" prop="reason">
                    <el-input
                        v-model="batchGeofenceStoreForm.reason"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入更换商家的原因（可选）"
                        maxlength="200"
                        show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchGeofenceStoreVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmBatchUpdateStoreByGeofence" :loading="batchGeofenceStoreLoading">确定</el-button>
            </div>
        </el-dialog>

        <!-- 订单操作记录弹窗 -->
        <el-dialog title="订单操作记录" :visible.sync="showOrderLogModal" width="50%" :before-close="closeOrderLogModal">
            <div v-loading="orderLogLoading" element-loading-text="加载中...">
                <div v-if="orderLogData && orderLogData.length > 0" class="order-log-content">
                    <div class="log-item" v-for="(logItem, index) in orderLogData" :key="index">
                        <div class="log-time">{{ logItem.createTime }}</div>
                        <div class="log-content">{{ logItem.storeMsgModel }}</div>
                        <div class="log-detail" v-if="logItem.content">{{ logItem.content }}</div>
                    </div>
                </div>
                <div v-else-if="!orderLogLoading" class="no-log">
                    <div class="no-log-text">暂无操作记录</div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeOrderLogModal">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { parseTime } from "@/utils/setMethods"
import { ordersource } from "@/data/common"
import ordermainupdatestore from "./ordermain-update-store"
import ordermainpinduoduorefund from "./ordermain-pinduoduorefund"
import ordermainupdatemark from "./ordermain-update-mark"
import ImagePreviewUpload from "@/components/ImagePreviewUpload"
export default {
    props: {},
    data() {
        return {
            printSdk: '',
            sfToken: '',
            // 超时统计相关
            timeoutStats: [
                { key: '1h', label: '1小时内', count: 0, hours: 1 },
                { key: '2h', label: '1-2小时', count: 0, hours: 2 },
                { key: '4h', label: '2-4小时', count: 0, hours: 4 },
                { key: '8h', label: '4-8小时', count: 0, hours: 8 },
                { key: '16h', label: '8-16小时', count: 0, hours: 16 },
                { key: '24h', label: '16-24小时', count: 0, hours: 24 },
                { key: '48h', label: '24-48小时', count: 0, hours: 48 },
                { key: '72h', label: '48-72小时', count: 0, hours: 72 },
                { key: '72h+', label: '72小时以上', count: 0, hours: 72 }
            ],
            // 超时未送达统计
            undeliveredTimeoutStats: [
                { key: '1h', label: '1小时内', count: 0, hours: 1 },
                { key: '2h', label: '1-2小时', count: 0, hours: 2 },
                { key: '4h', label: '2-4小时', count: 0, hours: 4 },
                { key: '8h', label: '4-8小时', count: 0, hours: 8 },
                { key: '16h', label: '8-16小时', count: 0, hours: 16 },
                { key: '24h', label: '16-24小时', count: 0, hours: 24 },
                { key: '48h', label: '24-48小时', count: 0, hours: 48 },
                { key: '72h', label: '48-72小时', count: 0, hours: 72 },
                { key: '72h+', label: '72小时以上', count: 0, hours: 72 }
            ],
            selectedTimeoutFilter: '', // 当前选中的超时筛选
            timeoutType: '', // 超时类型：'undelivered'(未配送) 或 'undelivered_timeout'(未送达)

            // 多级筛选相关数据
            showStoreDetails: false, // 是否显示水站详情
            storeTimeoutStats: [], // 按水站分组的超时统计数据
            selectedStoreId: '', // 当前选中的水站ID
            selectedStoreName: '', // 当前选中的水站名称
            detailsLoading: false, // 详情加载状态
            selectVal: '',
            backFilter: '', // 退款订单筛选
            storeList: [],
            adminStoreInfo: {},
            // 区域筛选相关
            areaOptions: [], // 区域选项数据
            areaFilter: [], // 区域筛选字段
            cascaderProps: {
                value: "citiesid",
                label: "citie",
                children: "cities",
                emitPath: true,
                checkStrictly: true // 允许选择任意级别
            },
            allStoreList: [], // 所有商家数据（用于区域筛选）
            filteredStoreList: [], // 筛选后的商家数据
            appkey: '',
            appkeylist: [
                { value: 'd794a292fc884a568a800d47ddaa5e01', label: '阿尔娃饮用水' },
                { value: '5c0f6c36b013486b8200ca0fa4a121e8', label: '好水送到家' },
                { value: '603690dcdf08490d9f134c29fe224248', label: '依美雪山' },
                { value: '06b1de86a7364508b3cc5d2e17bc6170', label: '飞蓝月泉' },
            ],
            ordersourcefilter: ordersource,
            ordermainupdatestoreVisible: false,
            ordermainpinduoduorefundVisible: false,
            ordermainupdatemarkVisible: false,
            // 批量操作相关
            selectedOrders: [], // 选中的订单
            batchMarkVisible: false, // 批量设置标记对话框
            batchMarkLoading: false, // 批量设置标记加载状态
            batchMarkForm: {
                mark: ''
            },
            batchMarkRules: {
                mark: [
                    { required: true, message: '请选择标记', trigger: 'change' }
                ]
            },
            // 批量围栏更换商家相关
            batchGeofenceStoreVisible: false, // 批量围栏更换商家对话框
            batchGeofenceStoreLoading: false, // 批量围栏更换商家加载状态
            batchGeofenceStoreForm: {
                reason: ''
            },
            batchGeofenceStoreRules: {
                // 围栏更换商家不需要必填验证，因为是自动判断
            },
            // 订单操作记录相关
            showOrderLogModal: false, // 是否显示操作记录弹窗
            orderLogData: null, // 操作记录数据列表
            orderLogLoading: false, // 操作记录加载状态
            currentOrderNum: '', // 当前查看的订单号
            fuckLoading10: false,
            // 图片预览相关
            imagePreviewVisible: false,
            previewImageList: [],
            currentImageIndex: 0,
            // 上传凭证相关
            uploadVoucherVisible: false,
            voucherUploading: false,
            voucherForm: {
                orderId: '',
                imageUrl: '',
                existingPics: [] // 存储已有的凭证图片
            },
            ordersource: '',
            username: '',
            userContent: '',
            startTime: '',
            endTime: '',
            storeId: '',
            dateTime: [],
            startTimeFinish: '',
            endTimeFinish: '',
            dateTimeFinish: [],
            orderZt: -1,
            page: 0,
            pageSize: 10,
            pageTotal: 0,
            currentPageSizes: [10, 15, 20], // 每页数据条数
            szmcorderlist: [],
            // 日期数据
            pickerOptions: {

                disabledDate(time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit("pick", [start, end])
                        }
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit("pick", [start, end])
                        }
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit("pick", [start, end])
                        }
                    }
                ]
            },
            selectOptions: [
                {
                    value: 2,
                    label: "待发单"
                },
                {
                    value: 3,
                    label: "待接单"
                },
                {
                    value: 4,
                    label: "已发货"
                },
                {
                    value: 10,
                    label: "已完成"
                },
                {
                    value: 8,
                    label: "退款(申请中)"
                },
                {
                    value: 11,
                    label: "退款(已解决)"
                },
            ],
        }
    },
    filters: {
        ordersourceFilter(v) {
            let value1 = ordersource.filter((e) => e.key == v);
            return value1.length > 0 ? value1[0].value : '';
        }
    },
    computed: {
        tableHeight() {
            // console.log(this.$store.getters.getGlobalHeight)
            let height = Number(this.$store.getters.getGlobalHeight) - 450
            if (height >= 300) {
                return height
            } else {
                return 300
            }
        },
        inTableHeight() {
            let height = this.$store.getters.getGlobalHeight
            if (height >= 400) {
                return parseInt(this.$util.mul(height, 0.5))
            } else {
                return 400
            }
        },
        // 上传时传递的额外数据
        uploadData() {
            return {
                orderId: this.voucherForm.orderId
            }
        }
    },
    created() {
        this.getszmcordermain()
        this.adminStoreInfo = JSON.parse(this.Cookies.get("adminStoreInfo"))
        this.getstore();
        this.getAreaOptions(); // 加载区域数据
        // this.initPrinter(); // 加载区域数据
    },
    mounted() { },
    methods: {

        // 下载面单PDF文件
        downloadWaybillPdf(url, token, filename) {
            // 创建一个临时的a标签来下载文件
            const link = document.createElement('a');
            link.style.display = 'none';

            // 使用fetch下载文件，添加授权头
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Auth-Token': token
                }
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            }).then(blob => {
                // 创建blob URL
                const blobUrl = window.URL.createObjectURL(blob);
                link.href = blobUrl;
                link.download = filename;

                // 添加到DOM并触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);

                this.$message.success(`面单文件 ${filename} 下载成功`);
            }).catch(error => {
                console.error('下载面单文件失败:', error);
                this.$message.error(`下载面单文件失败: ${error.message}`);
            });
        },

        initPrinter() {

            this.$post('/szmb/szmborder/sfexpress/gettoken').then((res) => {
                if (res.code == 1) {
                    this.sfToken = res.data;
                    const sdkCallback = result => { };
                    const sdkParams = {
                        env: "sbox", // 生产：pro；沙箱：sbox。不传默认生产，转生产需要修改这里
                        partnerID: "YQDZS7KIXFPK",
                        callback: sdkCallback,
                        notips: true
                    };
                    this.printSdk = new SCPPrint(sdkParams);

                    // 获取打印机列表
                    const getPrintersCallback = result => {
                        if (result.code === 1) {
                            const printers = result.printers;
                            console.log(printers)
                            // 设置默认打印机
                            // var printer = 0;
                            // selectElement.value = printer;
                            printSdk.setPrinter(printers[0]);
                        }
                    };
                    this.printSdk.getPrinters(getPrintersCallback);

                    // new SCPPrint({
                    //     partnerID: '',
                    //     env: 'sbox',
                    //     notips: true,
                    //     callback(result) {
                    //         if(result.code == 1) {
                    //             // 成功
                    //             const printers = result.printers;
                    //             console.log(printers)

                    //         }
                    //     }
                    // })
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        print(row) {
            // /szmb/szmborder/sfexpress/createorderfromid
            this.$get('/szmb/szmborder/sfexpress/createorderfromid', {
                orderId: row.orderId,
            }).then((res) => {
                if (res.code == 1) {
                    // 创建顺丰订单成功，继续打印面单
                    this.printWaybill(row);
                } else {
                    this.$message.error(res.msg)
                }
            }).catch((error) => {
                console.error('创建顺丰订单失败:', error);
                this.$message.error('创建顺丰订单失败');
            })
        },
        printWaybill(row) {
            
            this.$get('/szmb/szmborder/sfexpress/printwaybillbyorderid', {
                orderId: row.orderNumber,
            }).then((res) => {
                if (res.code == 1) {
                    // 获取打印面单成功，处理文件下载
                    const data = res.data;
                    if (data && data.fileUrls && data.fileUrls.length > 0) {
                        // 获取原始响应中的文件信息
                        const originalResponse = data.originalResponse;
                        if (originalResponse && originalResponse.obj && originalResponse.obj.files) {
                            // 遍历所有文件进行下载
                            originalResponse.obj.files.forEach((file, index) => {
                                if (file.url && file.token) {
                                    this.downloadWaybillPdf(file.url, file.token, `面单_${row.orderNumber}_${index + 1}.pdf`);
                                }
                            });
                        } else {
                            // 如果没有原始响应，使用简化的URL列表
                            data.fileUrls.forEach((url, index) => {
                                // 注意：这种情况下没有token，可能无法下载
                                this.$message.warning(`文件${index + 1}缺少授权token，无法下载`);
                            });
                        }
                    } else {
                        this.$message.warning('没有找到可下载的面单文件');
                    }
                } else {
                    this.$message.error(res.msg)
                }
            }).catch((error) => {
                console.error('打印面单请求失败:', error);
                this.$message.error('打印面单请求失败');
            })
        },

        // 计算超时统计
        calculateTimeoutStats() {
            // 重置统计数据
            this.timeoutStats.forEach(stat => {
                stat.count = 0
            })
            this.undeliveredTimeoutStats.forEach(stat => {
                stat.count = 0
            })

            // 遍历订单列表计算超时统计
            this.szmcorderlist.forEach(order => {
                // 统计未配送的订单
                if (this.isUndeliveredOrder(order)) {
                    const timeoutHours = this.calculateTimeoutHours(order)
                    this.categorizeTimeout(timeoutHours, 'undelivered')
                }

                // 统计未送达的订单（已发货但未签收/完成）
                if (this.isUndeliveredTimeoutOrder(order)) {
                    const timeoutHours = this.calculateUndeliveredTimeoutHours(order)
                    this.categorizeTimeout(timeoutHours, 'undelivered_timeout')
                }
            })
        },

        // 判断是否为未配送订单
        isUndeliveredOrder(order) {
            // 根据实际业务逻辑调整，这里假设状态为0-2为未配送
            // 您可能需要根据实际的订单状态字段调整这个判断
            return order.orderState !== undefined && (order.orderState === 0 || order.orderState === 1 || order.orderState === 2)
        },

        // 判断是否为未送达订单（已发货但未签收/完成）
        isUndeliveredTimeoutOrder(order) {
            // orderState=3且state!=3表示已发货但未签收
            // 或者其他表示已发货但未完成的状态
            return order.orderState !== undefined &&
                   ((order.orderState === 3 && order.state !== 3) ||
                    (order.orderState === 4 && order.state !== 3))
        },

        // 计算订单超时小时数（未配送订单）
        calculateTimeoutHours(order) {
            const now = new Date()
            let orderTime

            // 尝试解析订单创建时间（根据实际字段名调整）
            if (order.createTime) {
                orderTime = new Date(order.createTime)
            } else if (order.orderTime) {
                orderTime = new Date(order.orderTime)
            } else if (order.addTime) {
                orderTime = new Date(order.addTime)
            } else if (order.orderDate) {
                orderTime = new Date(order.orderDate)
            } else {
                return 0 // 如果没有时间字段，返回0
            }

            const diffMs = now - orderTime
            const diffHours = diffMs / (1000 * 60 * 60)
            return Math.max(0, diffHours)
        },

        // 计算未送达订单超时小时数（从发货时间开始计算）
        calculateUndeliveredTimeoutHours(order) {
            const now = new Date()
            let shipTime

            // 尝试解析发货时间（根据实际字段名调整）
            if (order.shipTime) {
                shipTime = new Date(order.shipTime)
            } else if (order.deliveryTime) {
                shipTime = new Date(order.deliveryTime)
            } else if (order.sendTime) {
                shipTime = new Date(order.sendTime)
            } else if (order.updateTime) {
                // 如果没有专门的发货时间，使用更新时间作为参考
                shipTime = new Date(order.updateTime)
            } else {
                // 如果没有发货时间，使用订单创建时间
                return this.calculateTimeoutHours(order)
            }

            const diffMs = now - shipTime
            const diffHours = diffMs / (1000 * 60 * 60)
            return Math.max(0, diffHours)
        },

        // 将超时小时数归类到对应的统计区间
        categorizeTimeout(hours, type = 'undelivered') {
            const statsArray = type === 'undelivered' ? this.timeoutStats : this.undeliveredTimeoutStats

            if (hours <= 1) {
                statsArray[0].count++  // 1小时内
            } else if (hours <= 2) {
                statsArray[1].count++  // 1-2小时
            } else if (hours <= 4) {
                statsArray[2].count++  // 2-4小时
            } else if (hours <= 8) {
                statsArray[3].count++  // 4-8小时
            } else if (hours <= 16) {
                statsArray[4].count++  // 8-16小时
            } else if (hours <= 24) {
                statsArray[5].count++  // 16-24小时
            } else if (hours <= 48) {
                statsArray[6].count++  // 24-48小时
            } else if (hours <= 72) {
                statsArray[7].count++  // 48-72小时
            } else {
                statsArray[8].count++  // 72小时以上
            }
        },

        // 按超时时间筛选订单 - 修改为显示水站详情并筛选订单
        filterByTimeout(timeoutKey, type = 'undelivered') {
            if (this.selectedTimeoutFilter === timeoutKey && this.timeoutType === type) {
                // 如果点击的是已选中的，则取消筛选
                this.clearTimeoutFilter()
                return
            }

            this.selectedTimeoutFilter = timeoutKey
            this.timeoutType = type
            this.selectVal = type == 'undelivered' ? 2 : 3;

            // 显示水站详情并同时筛选订单列表
            this.showStoreTimeoutDetails()

            // 重置到第一页并查询订单数据
            this.page = 0
            this.getszmcordermain()
        },

        // 清除超时筛选
        clearTimeoutFilter() {
            this.selectedTimeoutFilter = ''
            this.timeoutType = ''
            this.selectVal = ''
            this.showStoreDetails = false
            this.storeTimeoutStats = []
            this.selectedStoreId = ''
            this.selectedStoreName = ''
            this.storeId = '' // 清除水站筛选
            this.page = 0
            this.getszmcordermain()
        },

        // 获取超时统计数据（从后端）
        getTimeoutStats() {
            let that = this
            let params = {
                storeId: that.storeId,
                appkey: that.appkey,
                ordersource: that.ordersource,
                startTime: that.startTime,
                endTime: that.endTime
            }

            // 获取未配送超时统计
            that.$post('/szmcordermaincontroller/getTimeoutStats', {
                ...params,
                searchType: 'all'
            }).then((res) => {
                if (res.code === 1 && res.data) {
                    // 更新未配送统计数据
                    that.timeoutStats.forEach((stat) => {
                        if (res.data[stat.key] !== undefined) {
                            stat.count = res.data[stat.key]
                        }
                    })
                }
            }).catch((err) => {
                console.log('获取未配送超时统计失败:', err)
            })

            // 获取未送达超时统计
            that.$post('/szmcordermaincontroller/getUndeliveredTimeoutStats', {
                ...params,
                searchType: 'all'
            }).then((res) => {
                if (res.code === 1 && res.data) {
                    // 更新未送达统计数据
                    that.undeliveredTimeoutStats.forEach((stat) => {
                        if (res.data[stat.key] !== undefined) {
                            stat.count = res.data[stat.key]
                        }
                    })
                }
            }).catch((err) => {
                console.log('获取未送达超时统计失败:', err)
                // 如果后端接口不存在，使用前端计算
                that.calculateTimeoutStats()
            })
        },

        orderrefund(id) {
            // 先提示确认要不要退款
            this.$confirm('确定要退款吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.confirmrefund(id)
            })
        },
        updateOrderState(id) {
            let that = this
            // 先提示确认要不要退款
            this.$confirm('确定要恢复派送吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                that.$post("/szmb/orderreturn/adminupdatestatus2", {
                    orderMainId: id
                }).then((res) => {
                    if (res.code == 1) {
                        that.$message.success('恢复派送成功')
                        that.getszmcordermain()
                    }
                })
            })
        },
        confirmrefund(id) {
            let that = this
            that.$post("/szmb/orderreturn/adminreturn", {
                orderMainId: id
            }).then((res) => {
                if (res.code == 1) {
                    that.$message.success('退款成功')
                    that.getszmcordermain()
                }
            })
        },
        updatestore(id) {
            this.ordermainupdatestoreVisible = true
            this.$nextTick(() => {
                this.$refs.ordermainupdatestore.init(id)
            })
        },
        updateMark(orderId, currentMark) {
            this.ordermainupdatemarkVisible = true
            this.$nextTick(() => {
                this.$refs.ordermainupdatemark.init(orderId, currentMark)
            })
        },
        pinduoduorefund(id) {
            this.ordermainpinduoduorefundVisible = true
            this.$nextTick(() => {
                this.$refs.ordermainpinduoduorefund.init(id)
            })
        },
        selectDataDate(val) {
            if (val) {
                this.startTime = val[0] + " 00:00:00";
                this.endTime = val[1] + " 23:59:59";
            } else {
                this.startTime = ""
                this.endTime = ""
            }
        },
        selectDataDateFinish(val) {
            if (val) {
                this.startTimeFinish = val[0] + " 00:00:00";
                this.endTimeFinish = val[1] + " 23:59:59";
            } else {
                this.startTimeFinish = ""
                this.endTimeFinish = ""
            }
        },
        search() {
            this.page = 0;
            this.getszmcordermain();
        },
        clearSearch() {
            this.page = 0;
            this.startTime = ""
            this.appkey = ""
            this.endTime = ""
            this.startTimeFinish = ""
            this.endTimeFinish = ""
            this.username = ""
            this.userContent = ""
            this.storeId = ""
            this.ordersource = ""
            this.selectVal = ""
            this.backFilter = "" // 清除退款订单筛选
            this.selectedTimeoutFilter = "" // 清除超时筛选
            this.timeoutType = "" // 清除超时类型
            this.showStoreDetails = false // 隐藏水站详情
            this.storeTimeoutStats = []
            this.selectedStoreId = ''
            this.selectedStoreName = ''
            this.areaFilter = [] // 清除区域筛选
            this.filteredStoreList = this.allStoreList // 重置商家列表
            this.getszmcordermain();
        },
        // 分页 每页显示多少条
        handleSizeChange(val) {
            this.pageSize = val
            this.getszmcordermain()
        },
        // 点击的当前页
        handleCurrentChange(val) {
            this.page = (val -1)
            this.getszmcordermain()
        },


        exportHandle() {
            // 构建完整的导出参数，确保与查询条件保持一致
            var params = [
                "page=1",
                "pageSize=10000",
                "username=" + encodeURIComponent(this.username || ''),
                "userContent=" + encodeURIComponent(this.userContent || ''),
                "startTime=" + encodeURIComponent(this.startTime || ''),
                "endTime=" + encodeURIComponent(this.endTime || ''),
                "startTimeFinish=" + encodeURIComponent(this.startTimeFinish || ''),
                "endTimeFinish=" + encodeURIComponent(this.endTimeFinish || ''),
                "appkey=" + encodeURIComponent(this.appkey || ''),
                "ordersource=" + encodeURIComponent(this.ordersource || ''),
                "orderStatus=" + encodeURIComponent(this.selectVal || ''),
                "storeId=" + encodeURIComponent(this.storeId || ''),
                "timeoutFilter=" + encodeURIComponent(this.selectedTimeoutFilter || ''),
                "timeoutType=" + encodeURIComponent(this.timeoutType || ''),
                "back=" + encodeURIComponent(this.backFilter || ''), // 添加退款订单筛选参数
                "addressId=" // 保持与查询参数一致
            ];

            var url = this.$axios.adornUrl("/szmcordermaincontroller/exportplatformorder?" + params.join('&'));
            window.open(url);
        },
        getszmcordermain() {
            let that = this
            console.log(this.appkey)
            let isurl = "/szmcordermaincontroller/findallorderall"
            let o = {
                storeId: that.storeId,
                username: that.username,
                userContent: that.userContent,
                endTime: that.endTime,
                startTime: that.startTime,
                startTimeFinish: that.startTimeFinish,
                endTimeFinish: that.endTimeFinish,
                appkey: that.appkey,
                ordersource: that.ordersource,
                addressId: '',
                index: that.page,
                pageSize: that.pageSize,
                orderStatus: that.selectVal,
                timeoutFilter: that.selectedTimeoutFilter, // 添加超时筛选参数
                timeoutType: that.timeoutType, // 添加超时类型参数
                back: that.backFilter // 添加退款订单筛选参数
            }
            that.fuckLoading10 = true
            that.$post(isurl, o).then((res) => {
                that.fuckLoading10 = false
                if (res.code == 1) {
                    that.szmcorderlist = res.data.list
                    that.pageTotal = res.data.count

                    // 如果有超时筛选，计算每个订单的超时时长
                    if (that.selectedTimeoutFilter && that.szmcorderlist.length > 0) {
                        that.calculateOrderTimeouts()
                    }

                    // 更新超时统计（只在第一页且无超时筛选时更新）
                    if (that.page === 0 && !that.selectedTimeoutFilter) {
                        that.getTimeoutStats()
                    }
                } else {
                    that.szmcorderlist = []
                }
            })
        },
        pinduoduotest() {
            var that = this
            that.$get('/pdd/isvPageCode', {}).then((res) => {
                let result = res;


                PDD_OPEN_init({
                    code: result
                    // 对于获取 code 接口或未登录态，可不传 code：PDD_OPEN_init({}, function () { ... })
                }, function () {
                    // 初始化已完成
                    window.PDD_OPEN_getPati().then(
                        function (pati) {
                            console.log('拼多多用户信息', pati);

                            that.$post("/pdd/notify", {
                                "X-PDD-Pati": pati,
                                "X-PDD-PageCode": result,

                                header: 'json:pdd:' + pati + ':' + result,
                            }).then((res) => {
                                if (res.code == 1) {
                                    alert(res.data)
                                } else {
                                    alert('测试失败')
                                }
                            })
                            // 使用 pati
                        }).catch(error => console.log("拼多多用户信息获取失败", error))
                })
            })
        },
        getstore() {


            this.$post("/szmcstore/selectallstore", {
                storeId: this.adminStoreInfo.storeId
            }).then((res) => {
                if (res.code == 1) {
                    this.allStoreList = res.data
                    this.filteredStoreList = res.data // 初始化时显示所有商家
                    this.storeList = res.data // 保持原有的 storeList 用于其他功能
                } else {
                    this.allStoreList = []
                    this.filteredStoreList = []
                    this.storeList = []
                }
            })
        },
        // 获取区域选项数据
        getAreaOptions() {
            this.$post('/dpt/address/shanghai').then((res) => {
                if (res.code === 1) {
                    this.areaOptions = res.data
                }
            }).catch((err) => {
                console.log('加载区域数据失败:', err)
            })
        },
        // 区域筛选变化处理
        onAreaFilterChange() {
            this.storeId = '' // 清空已选择的商家
            this.filterStoresByArea()
        },
        // 根据区域筛选商家
        filterStoresByArea() {
            if (!this.areaFilter || this.areaFilter.length === 0) {
                // 没有选择区域时，显示所有商家
                this.filteredStoreList = this.allStoreList
                return
            }

            const [provinceId, cityId, areaId] = this.areaFilter

            this.filteredStoreList = this.allStoreList.filter(store => {
                // 根据选择的区域级别进行筛选
                if (areaId) {
                    // 选择了区/县，精确匹配区域
                    return store.storeArea === areaId.toString()
                } else if (cityId) {
                    // 选择了市，匹配城市
                    return store.storeCity === cityId.toString()
                } else if (provinceId) {
                    // 选择了省，匹配省份
                    return store.storeProvince === provinceId.toString()
                }
                return true
            })
        },
        // 处理picurl字符串，分割成数组
        getPicUrlArray(picurl) {
            if (!picurl || typeof picurl !== 'string') {
                return []
            }
            // 使用逗号分割字符串，并过滤掉空字符串
            return picurl.split(',').filter(url => url.trim() !== '')
        },
        // 预览图片
        previewImages(imageList, index) {
            this.$refs.imagePreviewUpload.previewImages(imageList, index)
        },
        // 处理图片加载错误
        handleImageError(event) {
            // 设置默认图片或隐藏图片
            event.target.style.display = 'none'
        },

        // 上传凭证相关方法
        uploadVoucher(orderId) {
            // 查找当前订单的凭证数据
            const currentOrder = this.szmcorderlist.find(order => order.orderId === orderId)
            let existingPics = []
            if (currentOrder && currentOrder.picurl) {
                existingPics = this.getPicUrlArray(currentOrder.picurl)
            }

            // 使用组件的方法打开上传对话框
            this.$refs.imagePreviewUpload.openUploadDialog(orderId, existingPics)
        },

        // 上传成功回调
        handleVoucherSuccess(response) {
            console.log('上传成功:', response)
            this.getszmcordermain() // 刷新订单列表
        },

        // 批量操作相关方法
        // 处理表格选择变化
        handleSelectionChange(selection) {
            this.selectedOrders = selection
        },

        // 清除选择
        clearSelection() {
            this.$refs.orderTable.clearSelection()
            this.selectedOrders = []
        },

        // 显示订单操作记录
        showOrderLog(orderNum) {
            if (!orderNum) {
                this.$message.error('订单号不能为空');
                return;
            }

            this.currentOrderNum = orderNum;
            this.showOrderLogModal = true;
            this.getOrderLog(orderNum);
        },

        // 获取订单操作记录
        getOrderLog(orderNum) {
            this.orderLogLoading = true;
            this.orderLogData = null;

            this.$post('/szmb/msg/selectmsgbyordernum', {
                orderNum: orderNum
            }).then((res) => {
                this.orderLogLoading = false;
                if (res.code == 1) {
                    // 如果返回的是单个对象，转换为数组
                    if (res.data && !Array.isArray(res.data)) {
                        this.orderLogData = [res.data];
                    } else {
                        this.orderLogData = res.data || [];
                    }
                } else {
                    this.orderLogData = null;
                    if (res.data !== '暂无数据') {
                        this.$message.error(res.data || '获取操作记录失败');
                    }
                }
            }).catch((error) => {
                this.orderLogLoading = false;
                this.orderLogData = null;
                this.$message.error('网络请求失败');
            });
        },

        // 关闭订单操作记录弹窗
        closeOrderLogModal() {
            this.showOrderLogModal = false;
            this.orderLogData = null;
            this.orderLogLoading = false;
            this.currentOrderNum = '';
        },

        // 批量设置标记
        batchUpdateMark() {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要设置标记的订单')
                return
            }
            this.batchMarkForm.mark = ''
            this.batchMarkVisible = true
            this.$nextTick(() => {
                this.$refs['batchMarkForm'].resetFields()
            })
        },

        // 确认批量设置标记
        confirmBatchUpdateMark() {
            this.$refs['batchMarkForm'].validate((valid) => {
                if (valid) {
                    const orderIds = this.selectedOrders.map(order => order.orderId).join(',')

                    this.batchMarkLoading = true
                    this.$post('/szmcordermaincontroller/updatebatchmark', {
                        orderIds: orderIds,
                        mark: this.batchMarkForm.mark
                    }).then((res) => {
                        this.batchMarkLoading = false
                        if (res.code === 1) {
                            this.$message.success(`成功为 ${this.selectedOrders.length} 个订单设置标记`)
                            this.batchMarkVisible = false
                            this.clearSelection()
                            this.getszmcordermain() // 刷新订单列表
                        } else {
                            this.$message.error('批量设置标记失败: ' + (res.msg || res.data || '未知错误'))
                        }
                    }).catch((err) => {
                        this.batchMarkLoading = false
                        console.error('批量设置标记失败:', err)
                        this.$message.error('批量设置标记失败')
                    })
                }
            })
        },

        // 批量围栏更换商家
        batchUpdateStoreByGeofence() {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要更换商家的订单')
                return
            }
            this.batchGeofenceStoreForm.reason = ''
            this.batchGeofenceStoreVisible = true
            this.$nextTick(() => {
                this.$refs['batchGeofenceStoreForm'].resetFields()
            })
        },

        // 确认批量围栏更换商家
        confirmBatchUpdateStoreByGeofence() {
            this.$refs['batchGeofenceStoreForm'].validate((valid) => {
                if (valid) {
                    const orderIds = this.selectedOrders.map(order => order.orderId).join(',')

                    this.batchGeofenceStoreLoading = true
                    this.$post('/szmcordermaincontroller/updatebatchstorebygeofence', {
                        orderIds: orderIds,
                        reason: this.batchGeofenceStoreForm.reason || '根据围栏自动更换商家'
                    }).then((res) => {
                        this.batchGeofenceStoreLoading = false
                        if (res.code === 1) {
                            this.$message.success(res.data || res.msg || '围栏更换商家操作完成')
                            this.batchGeofenceStoreVisible = false
                            this.clearSelection()
                            this.getszmcordermain() // 刷新订单列表
                        } else {
                            this.$message.error('批量围栏更换商家失败: ' + (res.msg || res.data || '未知错误'))
                        }
                    }).catch((err) => {
                        this.batchGeofenceStoreLoading = false
                        console.error('批量围栏更换商家失败:', err)
                        this.$message.error('批量围栏更换商家失败')
                    })
                } else {
                    return false
                }
            })
        },

        // 单个订单围栏更换商家
        updateStoreByGeofence(orderId) {
            this.$confirm('确定要根据围栏规则为此订单自动更换商家吗？', '确认操作', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '正在根据围栏规则判断商家...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                })

                this.$post('/szmcordermaincontroller/updatebatchstorebygeofence', {
                    orderIds: orderId.toString(),
                    reason: '根据围栏规则单个更换商家'
                }).then((res) => {
                    loading.close()
                    if (res.code === 1) {
                        this.$message.success(res.data || res.msg || '围栏更换商家操作完成')
                        this.getszmcordermain() // 刷新订单列表
                    } else {
                        this.$message.error('围栏更换商家失败: ' + (res.msg || res.data || '未知错误'))
                    }
                }).catch((err) => {
                    loading.close()
                    console.error('围栏更换商家失败:', err)
                    this.$message.error('围栏更换商家失败')
                })
            }).catch(() => {
                // 用户取消操作
            })
        },

        // 显示水站超时详情
        showStoreTimeoutDetails() {
            this.detailsLoading = true
            this.showStoreDetails = true

            let params = {
                storeId: this.storeId,
                appkey: this.appkey,
                ordersource: this.ordersource,
                startTime: this.startTime,
                endTime: this.endTime,
                timeoutFilter: this.selectedTimeoutFilter,
                timeoutType: this.timeoutType,
                searchType: 'planform'
            }

            this.$post('/szmcordermaincontroller/getStoreTimeoutStats', params).then((res) => {
                this.detailsLoading = false
                if (res.code === 1 && res.data) {
                    this.storeTimeoutStats = res.data
                } else {
                    this.storeTimeoutStats = []
                    this.$message.warning('暂无水站超时数据')
                }
            }).catch((err) => {
                this.detailsLoading = false
                console.log('获取水站超时统计失败:', err)
                this.$message.error('获取水站超时统计失败')
                this.storeTimeoutStats = []
            })
        },

        // 查看水站的详细订单
        viewStoreOrders(store) {
            this.selectedStoreId = store.storeId
            this.selectedStoreName = store.storeName

            // 设置水站筛选条件
            this.storeId = store.storeId.toString()

            // 隐藏水站详情，显示订单列表
            this.showStoreDetails = false

            // 重新查询订单数据
            this.page = 0
            this.getszmcordermain()
        },

        // 返回统计页面
        backToStats() {
            this.showStoreDetails = false
            this.selectedStoreId = ''
            this.selectedStoreName = ''
            this.storeTimeoutStats = []
            this.storeId = '' // 清除水站筛选
            this.page = 0
            this.getszmcordermain()
        },

        // 返回水站列表
        backToStoreList() {
            this.selectedStoreId = ''
            this.selectedStoreName = ''
            this.storeId = '' // 清除水站筛选
            this.showStoreDetails = true // 重新显示水站列表
            this.page = 0
            this.getszmcordermain()
        },

        // 计算订单列表中每个订单的超时时长
        calculateOrderTimeouts() {
            const currentTime = new Date()

            this.szmcorderlist.forEach(order => {
                if (order.orderDate || order.createTime) {
                    // 尝试解析订单创建时间
                    let orderTime
                    if (order.createTime) {
                        orderTime = new Date(order.createTime)
                    } else if (order.orderDate) {
                        orderTime = new Date(order.orderDate)
                    }

                    if (orderTime && !isNaN(orderTime.getTime())) {
                        const diffMs = currentTime - orderTime
                        const diffHours = diffMs / (1000 * 60 * 60)
                        order.timeoutHours = Math.max(0, diffHours)
                    } else {
                        order.timeoutHours = 0
                    }
                } else {
                    order.timeoutHours = 0
                }
            })
        },

        // 格式化超时时长显示
        formatTimeoutHours(hours) {
            if (!hours) return '0小时'

            const h = Math.floor(hours)
            const m = Math.floor((hours - h) * 60)

            if (h > 0 && m > 0) {
                return `${h}小时${m}分钟`
            } else if (h > 0) {
                return `${h}小时`
            } else if (m > 0) {
                return `${m}分钟`
            } else {
                return '不足1分钟'
            }
        },

        // 刷新电话功能
        refreshPhone(orderId) {
            let that = this
            // 设置当前订单的刷新状态
            const order = this.szmcorderlist.find(item => item.orderId === orderId)
            if (order) {
                this.$set(order, 'refreshing', true)
            }

            that.$get("/jddj/refreshPhone", { orderId: orderId }).then((res) => {
                // 清除刷新状态
                if (order) {
                    this.$set(order, 'refreshing', false)
                }

                if (res.code == 1) {
                    that.$message.success("刷新成功")
                    that.getszmcordermain() // 刷新订单列表
                } else {
                    that.$message.error("刷新失败: " + (res.msg || res.data || '未知错误'))
                }
            }).catch((err) => {
                // 清除刷新状态
                if (order) {
                    this.$set(order, 'refreshing', false)
                }
                console.error('刷新电话失败:', err)
                that.$message.error('刷新电话失败')
            })
        }
    },
    filters: {
        timestampToTime(val, timestamp) {
            return parseTime(val, timestamp)
        }
    },
    components: {
        ordermainupdatestore,
        ordermainpinduoduorefund,
        ordermainupdatemark,
        ImagePreviewUpload,
        Upload: () => import('@/components/upload')
    }
}
</script>

<style scoped>
/* 超时统计卡片样式 */
.timeout-stats-container {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-card:hover {
    border-color: #409EFF !important;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2) !important;
    transform: translateY(-1px);
}

.stat-card.active {
    border-color: #409EFF !important;
    background: #ecf5ff !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
}

.stat-card.active .stat-count {
    color: #409EFF !important;
}

.stat-card.active .stat-label {
    color: #409EFF !important;
    font-weight: bold;
}

/* 未送达统计卡片特殊样式 */
.undelivered-stat-card:hover {
    border-color: #f56c6c !important;
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.2) !important;
    transform: translateY(-1px);
}

.undelivered-stat-card.active {
    border-color: #f56c6c !important;
    background: #fef0f0 !important;
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;
}

.undelivered-stat-card.active .stat-count {
    color: #f56c6c !important;
}

.undelivered-stat-card.active .stat-label {
    color: #f56c6c !important;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-cards {
        flex-direction: column;
    }

    .stat-card {
        min-width: auto !important;
    }
}
</style>

<style lang="scss" scoped>
.headBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
}

.content-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.content-item1 {
    display: flex;
    justify-content: flex-start;

    div {
        width: 120px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border: 1px solid #d8dce5;
        border-bottom: 1px solid #eff2f7;
        background: #eff2f7;
        cursor: pointer;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #949fa8;

        &:first-child {
            border-right: none;
            border-radius: 4px 0px 0px 0px;
        }

        &:nth-child(2) {
            border-right: none;
            border-radius: 0px 0px 0px 0px;
        }

        &:last-child {
            border-radius: 0px 4px 0px 0px;
        }
    }

    .active-item {
        background: #ffffff;
        border-bottom: 1px solid #ffffff;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #1693fd;
    }
}

.content-item2 {
    // width: 600px;
    height: 32px;
    line-height: 32px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    div {
        margin-left: 20px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #3d4c66;
    }
}

.cont-cent {
    width: 100%;
    border: 1px solid #d8dce5;
    margin-top: -1px;
    border-radius: 0 4px 4px 4px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    ::v-deep .el-form {
        margin: 0 !important;
    }

    ::v-deep .el-form-item--small.el-form-item {
        margin-bottom: 0;
    }
}

.royalty-cont {
    padding: 15px 0;
}

// 新增  设计表格 开始---------------------------------
.tableBox {
    .el-input-price {
        width: 120px;
    }
}

.table-head-box {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    background: #eff2f7;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        &:nth-child(1) {
            width: 36%;
            min-width: 150px;
        }

        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5) {
            width: 16%;
            min-width: 120px;
        }

        &:last-child {
            border-right: none;
        }
    }
}

// 设计表格
.table-box-design {
    width: 100%;
    box-sizing: border-box;

    .table-item {
        width: 100%;
        border: 1px solid #d8dce5;
        margin-bottom: 10px;
        box-sizing: border-box;

        .table-item-header {
            width: 100%;
            height: 40px;
            line-height: 40px;
            background: #eff2f7;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-sizing: border-box;

            .table-header-input {
                margin-left: 20px;
                display: flex;

                .save-item {
                    margin-left: 10px;
                }
            }

            .table-item-bname {
                min-width: 220px;
            }
        }

        .table-body-line {
            width: 100%;
            display: flex;
            justify-content: space-between;
            min-height: 45px;
            border-top: 1px solid #d8dce5;
            box-sizing: border-box;

            ::v-deep .el-input.is-disabled .el-input__inner {
                background-color: #ffffff;
                border-color: #e4e7ed;
                color: #3d4c66;
                cursor: not-allowed;
            }

            &>div {
                border-right: 1px solid #d8dce5;
                box-sizing: border-box;
                color: #3d4c66;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;

                &:nth-child(1) {
                    width: 36%;
                    min-width: 150px;
                }

                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4),
                &:nth-child(5) {
                    width: 16%;
                    min-width: 120px;
                }

                &:last-child {
                    border-right: none;
                }
            }
        }
    }
}

::v-deep {
    .el-scrollbar {
        width: 100%;
        overflow: hidden;
        height: 100%;
    }

    .el-scrollbar__wrap {
        overflow: scroll;
        overflow-x: auto;
        height: 100%;
    }

    .el-scrollbar__wrap.default-scrollbar__wrap {
        overflow-x: hidden;
    }
}

.empty-data {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    font-size: 16px;
    text-align: center;
    border: 1px solid #d8dce5;
    color: #a5aeb5;
}

.total-box {
    margin-top: 20px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        &:nth-child(1) {
            width: 36%;
            min-width: 150px;
        }

        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5) {
            width: 16%;
            min-width: 120px;
        }

        &:last-child {
            border-right: none;
        }
    }
}

// 设计表格 结束
.pages-box {
    width: 100%;
    padding: 30px 0 0px;
    text-align: center;
}

.total-box2 {
    margin-top: 20px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;

        &:nth-child(1) {
            width: 60px;
        }

        &:last-child {
            border-right: none;
            padding: 0 25px;

            &>span {
                margin-left: 20px;
            }
        }
    }
}

::v-deep .el-tabs__nav-wrap::after {
    height: 1px;
}

// 新加样式
.tab-item-ul {
    width: 100%;
    padding: 20px 0 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item-li {
        height: 30px;
        line-height: 30px;
        text-align: center;
        box-sizing: border-box;
        font-size: 16px;
        color: #3d4c66;
        cursor: pointer;
    }

    .tab-item-li-active {
        border-bottom: 3px solid #1693fd;
        color: #1693fd;
    }

    .tab-item-line {
        width: 2px;
        height: 20px;
        background: #3d4c66;
        margin: 0 10px;
    }
}

.sp-cont {
    .content-box {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }

    .content-item2 {
        // width: 600px;
        height: 32px;
        line-height: 32px;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        div {
            margin-left: 20px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #3d4c66;
        }
    }

    .cont-cent {
        width: 100%;
        border: 1px solid #d8dce5;
        margin-top: -1px;
        border-radius: 0 4px 4px 4px;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        ::v-deep .el-form {
            margin: 0 !important;
        }

        ::v-deep .el-form-item--small.el-form-item {
            margin-bottom: 0;
        }
    }

    ::v-deep {
        .el-scrollbar {
            width: 100%;
            overflow: hidden;
            height: 100%;
        }

        .el-scrollbar__wrap {
            overflow: scroll;
            overflow-x: auto;
            height: 100%;
        }

        .el-scrollbar__wrap.default-scrollbar__wrap {
            overflow-x: hidden;
        }
    }

    .total-box {
        margin-top: 20px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        border: 1px solid #d8dce5;
        background: #fafafa;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;

        div {
            border-right: 1px solid #d8dce5;
            box-sizing: border-box;
            color: #3d4c66;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;

            &:nth-child(1) {
                width: 36%;
                min-width: 150px;
            }

            &:nth-child(2),
            &:nth-child(3),
            &:nth-child(4),
            &:nth-child(5) {
                width: 16%;
                min-width: 120px;
            }

            &:last-child {
                border-right: none;
            }
        }
    }

    // 设计表格 结束
    .pages-box {
        width: 100%;
        padding: 30px 0 0px;
        text-align: center;
    }

    .total-box2 {
        margin-top: 20px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        border: 1px solid #d8dce5;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;

        div {
            border-right: 1px solid #d8dce5;
            box-sizing: border-box;
            color: #3d4c66;
            display: flex;
            align-items: center;
            justify-content: center;

            &:nth-child(1) {
                width: 60px;
            }

            &:last-child {
                border-right: none;
                padding: 0 25px;

                &>span {
                    margin-left: 20px;
                }
            }
        }
    }

    .goodsCard {
        width: 100%;
        padding: 0 15px;
        box-sizing: border-box;
        margin-bottom: 20px;
    }

    .goodsCard>div:nth-child(1) {
        width: 30%;
    }

    .goodsCard>div:nth-child(2) {
        width: 70%;
    }

    .el-card__header {
        padding: 0;
    }

    .detailBox {
        padding: 30px 0px;
        box-sizing: border-box;
        display: flex;
    }

    .box-card {
        width: 49%;
    }

    ::v-deep.el-radio__inner {
        width: 20px;
        height: 20px;
    }

    .clearfix {
        font-weight: bold;
    }

    ::v-deep.el-badge__content.is-fixed {
        right: 0;
    }

    .moneyBox>div {
        width: 250px;
    }

    .radioGroup ::v-deep.el-radio-button--small .el-radio-button__inner {
        padding: 9px 16px;
    }

    .radioGroup ::v-deep.el-badge__content.is-fixed {
        right: 4px;
    }
}

.total-box66 {
    margin-top: 20px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #eff2f7;
    display: flex;
    justify-content: space-between;

    div {
        &:first-child {
            width: 55px;
            text-align: center;
        }

        &:last-child {
            padding: 0 20px;

            span {
                margin-left: 25px;
            }
        }
    }
}

.detailDialog ::v-deep .el-dialog .el-dialog__body {
    padding: 10px 20px 30px;
}

.water-ticket-total {
    margin-top: 20px;
    margin-bottom: -20px;
    text-align: right;
}

// 图片相关样式
// 图片预览按钮样式
.pic-preview-btn-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pic-preview-btn {
    font-size: 12px;
    padding: 4px 8px;
    color: #409eff;

    &:hover {
        color: #66b1ff;
    }

    .el-icon-picture-outline {
        margin-right: 4px;
    }
}

.image-preview-container {
    text-align: center;

    .preview-image {
        max-width: 100%;
        max-height: 400px;
        object-fit: contain;
    }

    .single-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 400px;
    }
}

::v-deep .el-carousel__container {
    height: 400px;
}

::v-deep .el-carousel__item {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 上传凭证样式 */
.upload-voucher-container {
    padding: 20px 0;
}

.voucher-uploader {
    display: inline-block;
}

.voucher-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voucher-uploader .el-upload:hover {
    border-color: #409EFF;
}

.voucher-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
}

.voucher-image {
    width: 200px;
    height: 200px;
    object-fit: cover;
    display: block;
}

.upload-tip {
    margin-top: 10px;
    font-size: 12px;
    color: #999;
}

/* 已有凭证样式 */
.existing-vouchers {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.existing-voucher-item {
    position: relative;
    display: inline-block;
}

.existing-voucher-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid #e6e6e6;
    transition: all 0.3s ease;
}

.existing-voucher-image:hover {
    border-color: #409EFF;
    transform: scale(1.05);
}

.voucher-index {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #409EFF;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.existing-tip {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 标记样式 */
.mark-text {
    color: #409EFF;
    cursor: pointer;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    transition: all 0.3s ease;
}

.mark-text:hover {
    background: #409EFF;
    color: white;
}

.mark-placeholder {
    color: #999;
    cursor: pointer;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px dashed #ddd;
    transition: all 0.3s ease;
}

.mark-placeholder:hover {
    color: #409EFF;
    border-color: #409EFF;
}

/* 水站详情样式 */
.store-item:hover {
    border-color: #409EFF !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2) !important;
    transform: translateY(-1px);
}

.store-details-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
}

.details-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* 订单操作记录按钮样式 */
.order-log-btn {
    color: #67c23a;
    cursor: pointer;
    transition: all 0.3s;
    padding: 2px 0;
    font-size: 12px;
}

.order-log-btn:hover {
    color: #5daf34;
    text-decoration: underline;
}

/* 订单操作记录弹窗样式 */
.order-log-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}

.log-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #409eff;
    position: relative;
    transition: all 0.3s;
}

.log-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.log-item:last-child {
    margin-bottom: 0;
}

.log-time {
    font-size: 12px;
    color: #409eff;
    margin-bottom: 8px;
    font-weight: 500;
}

.log-content {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
}

.log-detail {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    background: #f0f0f0;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
    border-left: 2px solid #e0e0e0;
}

.no-log {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.no-log-text {
    font-size: 14px;
}
</style>
