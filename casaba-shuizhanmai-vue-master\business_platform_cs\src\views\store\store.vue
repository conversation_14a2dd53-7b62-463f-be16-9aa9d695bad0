<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="商户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.role1">
          <el-option label="全部(角色)" value=""></el-option>
          <el-option v-for="item in role" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.identity">
          <el-option label="全部(身份)" value=""></el-option>
          <el-option v-for="item in identity" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.state">
          <el-option label="全部(状态)" value=""></el-option>
          <el-option v-for="item in state" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="adminStoreInfo.role1 == 4">
        <el-cascader
          v-model="dataForm.areaFilter"
          placeholder="全部(区域)"
          :options="areaOptions"
          :props="cascaderProps"
          filterable
          clearable
          style="width: 200px;">
        </el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.sort" @change="onSearch()" placeholder="排序方式" style="width: 120px;">
          <el-option label="默认排序" :value="1"></el-option>
          <el-option label="未送达降序" :value="2"></el-option>
          <el-option label="新店铺在前" :value="3"></el-option>
        </el-select>
              </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addStoreHandle()" v-if="adminStoreInfo.role1 != 0">新增店铺</el-button>
        <!-- <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
      </el-form-item>
    </el-form>
    <div class="royalty-cont">
    <el-table :data="dataList"  :max-height="tableHeight - 80"  border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;" size="mini">
      <!-- <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column> -->
      <el-table-column prop="storeId" header-align="center" align="center" label="商户ID">
      </el-table-column>
      <el-table-column prop="storeAme" header-align="center" align="center" label="商户名称">
      </el-table-column>
      <el-table-column prop="storePhone" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="storeDetailed" header-align="center" align="center" label="商户地址">
      </el-table-column>
      <el-table-column prop="nodeal" header-align="center" align="center" label="未送达订单">
      </el-table-column>
      <el-table-column prop="userCount" header-align="center" align="center" label="总客户数">
      </el-table-column>
      <el-table-column prop="moneyCount" header-align="center" align="center" label="总营业额">
        <div slot-scope="scope">{{ scope.row.moneyCount | SumFormat }}</div>
      </el-table-column>
      <el-table-column prop="moneyCountToday" header-align="center" align="center" label="今日营业额">
        <div slot-scope="scope">{{ scope.row.moneyCountToday | SumFormat }}</div>
      </el-table-column>
      <el-table-column prop="moneyCountToday" header-align="center" align="center" label="今日现金收入">
        <div slot-scope="scope">{{ scope.row.moneyAllToday | SumFormat }}</div>
      </el-table-column>
      <el-table-column prop="pledgeCount" header-align="center" align="center" label="总押桶">
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="身份">
        <div slot-scope="scope">
          <!-- <el-tag type="primary" @click="addOrUpdateHandle(scope.row.storeId)" -->
          <el-tag type="primary" :class="('tag-color-mini tag-color-' + scope.row.identity)">{{ scope.row.identity ==
      null ? '空' : identity[scope.row.identity].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="状态">
        <div slot-scope="scope">
          <el-tag type="primary" @click="storeupdatestate(scope.row.storeId)"
            :class="('tag-color-mini tag-color-' + scope.row.state)">{{ scope.row.state | stateFilter }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="水票">
        <div slot-scope="scope">
          <el-tag type="primary" @click="storeupdateshuipiao(scope.row.storeId)" v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
            :class="('tag-color-mini tag-color-' + scope.row.shuipiao)">{{ scope.row.shuipiao ==
      null ? '空' : stateOnly[scope.row.shuipiao].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="押桶">
        <div slot-scope="scope">
          <el-tag type="primary" @click="storeupdateyatong(scope.row.storeId)" v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
            :class="('tag-color-mini tag-color-' + scope.row.yatong)">{{ scope.row.yatong ==
      null ? '空' : stateOnly[scope.row.yatong].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="联营商品">
        <div slot-scope="scope">
          <el-tag type="primary" @click="storeupdatelianying(scope.row.storeId)" v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
            :class="('tag-color-mini tag-color-' + scope.row.lianying)">{{ scope.row.lianying ==
      null ? '空' : stateOnly[scope.row.lianying].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="identity" header-align="center" align="center" width="120" label="返还返现金">
        <div slot-scope="scope">
          <div v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)">
          <el-tag type="primary" @click="storeupdatejifen(scope.row.storeId)"
            :class="('tag-color-mini tag-color-' + scope.row.integralState)">{{ scope.row.integralState ==
      null ? '空' : stateOnly[scope.row.integralState].value }}</el-tag>
        <div v-if="scope.row.integralState">返还比例：{{ scope.row.distribution }}</div>
        </div>
        <div v-else>
          <div>
            -
          </div>
        </div>
        </div>
      </el-table-column>
      <el-table-column prop="isphoto" header-align="center" align="center" width="120" label="强制拍照">
        <div slot-scope="scope">
          <el-tag type="primary" @click="storeupdateisphoto(scope.row.storeId)" v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
            :class="('tag-color-mini tag-color-' + scope.row.isphoto)">{{ scope.row.isphoto ==
      null ? '空' : stateOnly[scope.row.isphoto].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="角色" header-align="center" align="center" width="160" label="角色与区域">
        <div slot-scope="scope">
          <!-- 角色标签 -->
          <div style="margin-bottom: 5px;">
            <el-tag v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
                    type="primary"
                    @click="storeupdaterolearea(scope.row.storeId)"
                    :class="('tag-color-mini tag-color-' + scope.row.role1)"
                    style="cursor: pointer;">
              {{ scope.row.role1 == null ? '空' : role[scope.row.role1].value }}
            </el-tag>
            <el-tag v-else type="primary"
                    :class="('tag-color-mini tag-color-' + scope.row.role1)">
              {{ scope.row.role1 == null ? '空' : role[scope.row.role1].value }}
            </el-tag>
          </div>

          <!-- 区域信息 -->
          <div v-if="scope.row.role1 == 2 || scope.row.role1 == 3" style="font-size: 12px;">
            <div v-if="scope.row.role1 == 2">
              <!-- 城市合伙人 -->
              <div v-if="scope.row.storeCityName" style="color: #606266;">
                {{ scope.row.storeProvinceName + "-" + scope.row.storeCityName }}
              </div>
              <div v-else style="color: #f56c6c; cursor: pointer;" @click="storeupdaterolearea(scope.row.storeId)">
                <i class="el-icon-warning"></i> 未设置区域
              </div>
            </div>
            <div v-if="scope.row.role1 == 3">
              <!-- 区域合伙人 -->
              <div v-if="scope.row.storeAreaName" style="color: #606266;">
                {{ scope.row.storeProvinceName + "-" + scope.row.storeCityName + "-" + scope.row.storeAreaName }}
              </div>
              <div v-else style="color: #f56c6c; cursor: pointer;" @click="storeupdaterolearea(scope.row.storeId)">
                <i class="el-icon-warning"></i> 未设置区域
              </div>
            </div>
          </div>

          <!-- 快捷操作按钮 -->
          <div v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)"
               style="margin-top: 5px;">
            <el-button-group>
              <el-button size="mini" type="text" @click="quickSetRole(scope.row.storeId, 0)"
                         v-if="scope.row.role1 != 0" title="设为普通商户">普通</el-button>
              <el-button size="mini" type="text" @click="quickSetRole(scope.row.storeId, 2)"
                         v-if="scope.row.role1 != 2" title="设为城市合伙人">城市</el-button>
              <el-button size="mini" type="text" @click="quickSetRole(scope.row.storeId, 3)"
                         v-if="scope.row.role1 != 3" title="设为区域合伙人">区域</el-button>
            </el-button-group>
          </div>
        </div>
      </el-table-column>
      <el-table-column prop="连锁商户" header-align="center" align="center" width="120" label="连锁商户">
        <div slot-scope="scope">
          <div v-if="scope.row.role1 == 0">
            <div @click="storeupdatepid(scope.row.storeId)" v-if="scope.row.pid">{{ scope.row.pname }}</div>
            <el-button type="text" size="small" @click="storeupdatepid(scope.row.storeId)" v-else>关联店铺</el-button>
          </div>
          <div v-if="scope.row.role1 == 1">
            <el-button type="text" size="small" @click="storedetailchild(scope.row.storeId)">查看连锁商户</el-button>
          </div>
        </div>
      </el-table-column>
      <el-table-column prop="所属区域" header-align="center" align="center" width="160" label="商户所属区域">
        <div slot-scope="scope" class="store-area-display">
          <div v-if="scope.row.storeProvinceName || scope.row.storeCityName || scope.row.storeAreaName"
               style="cursor: pointer;"
               @click="storeupdatestorearea(scope.row.storeId)"
               title="点击修改所属区域">
            <div class="area-text">{{ getStoreAreaDisplay(scope.row) }}</div>
            <el-button size="mini" type="text" class="edit-btn">
              <i class="el-icon-edit"></i> 修改
            </el-button>
          </div>
          <div v-else class="warning-text" style="cursor: pointer;" @click="storeupdatestorearea(scope.row.storeId)">
            <div><i class="el-icon-warning"></i> 未设置区域</div>
            <el-button size="mini" type="text" class="set-btn">
              点击设置
            </el-button>
          </div>
        </div>
      </el-table-column>
      <el-table-column prop="操作" header-align="center" align="center" width="200" label="操作" fixed="right">
        <div slot-scope="scope">
          <div v-if="adminStoreInfo.role1 == 4 || (adminStoreInfo.role1 == 5 && scope.row.storeCity == adminStoreInfo.storeCity)">
          <el-button type="text" size="small" style="color: red;" @click="tuitong(scope.row.storeId)"
            v-if="scope.row.state == 2">退桶情况</el-button>
          <el-button type="text" size="small" style="color: red;" @click="storeupdatefenzhang(scope.row.storeId)"
            v-if="scope.row.state == 2 && scope.row.role1 == 0">分账配置</el-button>
          <!-- <el-button type="text" size="small" style="color: red;" @click="storeupdategeofence(scope.row.storeId)"
            v-if="scope.row.state == 2">地理围栏</el-button> -->
          <el-button type="text" size="small" style="color: red;" @click="storeupdatestate(scope.row.storeId, 3)"
            v-if="scope.row.state == 2">禁用</el-button>
          <el-button type="text" size="small" @click="storeupdatestate(scope.row.storeId, 2)" v-else>启用</el-button>
          <el-button type="text" size="small" style="color: blue;" @click="inStore(scope.row.storeId)">进入店铺</el-button>
        </div>
        <div v-else>
          <el-button type="text" size="small" style="color: blue;" @click="inStore(scope.row.storeId)">进入店铺</el-button>
          <!-- <el-button type="text" size="small" style="color: red;" @click="storeupdategeofence(scope.row.storeId)"
            v-if="scope.row.state == 2">地理围栏</el-button> -->
        </div>

        </div>
      </el-table-column>
      <!-- <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
</el-table-column> -->
    </el-table>
  </div>
  <div class="padding flex align-items-center justify-content-center">
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <storeupdatestate v-if="storeupdatestateVisible" ref="storeupdatestate" @refreshDataList="getDataList">
    </storeupdatestate>
    <storeupdateshuipiao v-if="storeupdateshuipiaoVisible" ref="storeupdateshuipiao" @refreshDataList="getDataList">
    </storeupdateshuipiao>
    <storeupdateyatong v-if="storeupdateyatongVisible" ref="storeupdateyatong" @refreshDataList="getDataList">
    </storeupdateyatong>
    <storeupdaterole v-if="storeupdateroleVisible" ref="storeupdaterole" @refreshDataList="getDataList">
    </storeupdaterole>
    <storeupdatepid v-if="storeupdatepidVisible" ref="storeupdatepid" @refreshDataList="getDataList"></storeupdatepid>
    <storedetailchild v-if="storedetailchildVisible" ref="storedetailchild" @refreshDataList="getDataList">
    </storedetailchild>
    <storeupdatearea v-if="storeupdateareaVisible" ref="storeupdatearea" @refreshDataList="getDataList">
    </storeupdatearea>
    <storeupdatefenzhang v-if="storeupdatefenzhangVisible" ref="storeupdatefenzhang" @refreshDataList="getDataList">
    </storeupdatefenzhang>
    <storeupdatelianying v-if="storeupdatelianyingVisible" ref="storeupdatelianying" @refreshDataList="getDataList">
    </storeupdatelianying>
    <storeupdatejifen v-if="storeupdatejifenVisible" ref="storeupdatejifen" @refreshDataList="getDataList">
    </storeupdatejifen>
    <storeupdategeofence v-if="storeupdategeofenceVisible" ref="storeupdategeofence" @refreshDataList="getDataList">
    </storeupdategeofence>
    <storeupdateisphoto v-if="storeupdateisphotoVisible" ref="storeupdateisphoto" @refreshDataList="getDataList">
    </storeupdateisphoto>
    <storeupdaterolearea v-if="storeupdateroleareaVisible" ref="storeupdaterolearea" @refreshDataList="getDataList">
    </storeupdaterolearea>
    <storeupdatestorearea v-if="storeupdatestoreareaVisible" ref="storeupdatestorearea" @refreshDataList="getDataList">
    </storeupdatestorearea>
    <store-add v-if="storeAddVisible" ref="storeAdd" @refreshDataList="getDataList"></store-add>
  </div>
</template>

<script>
import { role, identity, state, stateOnly } from '@/data/store'
import AddOrUpdate from './store-update-identity'
import storeupdatestate from './store-update-state'
import storeupdateshuipiao from './store-update-shuipiao'
import storeupdateyatong from './store-update-yatong'
import storeupdaterole from './store-update-role'
import storeupdatepid from './store-update-pid'
import storedetailchild from './store-detailchild'
import storeupdatearea from './store-update-area'
import storeupdatefenzhang from './store-update-fenzhang'
import storeupdatelianying from './store-update-lianying'
import storeupdatejifen from './store-update-jifen'
import storeupdategeofence from './store-geofence'
import storeupdateisphoto from './store-update-isphoto'
import storeupdaterolearea from './store-update-role-area'
import storeupdatestorearea from './store-update-storearea'
import StoreAdd from './store-add'
export default {
  data() {
    return {
      adminStoreInfo: {},
      stateOnly,
      state,
      role,
      identity,
      areaOptions: [], // 区域选项数据
      cascaderProps: {
        value: "citiesid",
        label: "citie",
        children: "cities",
        emitPath: true,
        checkStrictly: true // 允许选择任意级别
      },
      dataForm: {
        name: '',
        mobile: '',
        role1: '',
        identity: '',
        state: 2,
        sort: 1,
        areaFilter: [], // 区域筛选字段
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      storeupdatestateVisible: false,
      storeupdateshuipiaoVisible: false,
      storeupdateyatongVisible: false,
      storeupdateroleVisible: false,
      storeupdatepidVisible: false,
      storedetailchildVisible: false,
      storeupdateareaVisible: false,
      storeupdatefenzhangVisible: false,
      storeupdatelianyingVisible: false,
      storeupdatejifenVisible: false,
      storeupdategeofenceVisible: false,
      storeupdateisphotoVisible: false,
      storeupdateroleareaVisible: false,
      storeupdatestoreareaVisible: false,
      storeAddVisible: false,
    }
  },
  filters: {
    stateFilter(v) {
      let value1 = state.filter((e) => e.key == v);
      return value1.length > 0 ? value1[0].value : '';
    }
  },
  components: {
    AddOrUpdate,
    storeupdatestate,
    storeupdateshuipiao,
    storeupdateyatong,
    storeupdaterole,
    storeupdatepid,
    storedetailchild,
    storeupdatearea,
    storeupdatefenzhang,
    storeupdatelianying,
    storeupdatejifen,
    storeupdategeofence,
    storeupdateisphoto,
    storeupdaterolearea,
    storeupdatestorearea,
    StoreAdd,
  },
  computed: {
    tableHeight() {
      let height = Number(this.$store.getters.getGlobalHeight) - 200
      if (height >= 500) {
        return height
      } else {
        return 500
      }
    }
  },
  mounted() {
    this.adminStoreInfo = JSON.parse(this.Cookies.get("adminStoreInfo"))
    console.log(this.adminStoreInfo)
    this.getAreaOptions() // 加载区域数据
    this.getDataList()
  },
  methods: {
    tuitong(v) {
      this.$router.push({
        name: "tuitong",
        query: { storeId: v }
      })
    },
    inStore(v) {

      this.Cookies.set("storeId", v);
      let info = this.dataList.filter(e => { e.storeId == v })[0];
      this.Cookies.set("storeInfo", info);
      this.$router.replace({
        name: 'home'
      })
      location.reload();
    },
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      // 处理区域筛选参数
      let areaParams = {}
      if (this.dataForm.areaFilter && this.dataForm.areaFilter.length > 0) {
        areaParams.storeProvince = this.dataForm.areaFilter[0] || ''
        areaParams.storeCity = this.dataForm.areaFilter[1] || ''
        areaParams.storeArea = this.dataForm.areaFilter[2] || ''
      }

      this.$post('szmcstore/list', {
        'pageNo': this.pageIndex,
        'pageSize': this.pageSize,
        'name': this.dataForm.name,
        'mobile': this.dataForm.mobile,
        'role1': this.dataForm.role1,
        'identity': this.dataForm.identity,
        'state': this.dataForm.state,
        'sort': this.dataForm.sort,
        'adminStoreId': this.adminStoreInfo.storeId,
        ...areaParams // 展开区域筛选参数
      }).then((res) => {
        this.dataListLoading = false
        if (res.code === 1) {
          this.dataList = res.data.list
          this.totalPage = res.data.count
        } else {
          this.dataList = []
          this.totalPage = 0
        }
      })
    },
    // 获取区域选项数据
    getAreaOptions() {
      this.$post('/dpt/address/shanghai').then((res) => {
        if (res.code === 1) {
          this.areaOptions = res.data
        }
      }).catch((err) => {
        console.log('加载区域数据失败:', err)
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 新增店铺
    addStoreHandle() {
      this.storeAddVisible = true
      this.$nextTick(() => {
        // 如果是连锁店铺（role1 = 1），传入当前用户的storeId作为pid
        const pid = this.adminStoreInfo.role1 === 1 ? this.adminStoreInfo.storeId : null
        this.$refs.storeAdd.init(pid)
      })
    },
    storeupdatestate(id, state) {
      this.storeupdatestateVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatestate.init(id, state)
      })
    },
    storeupdateshuipiao(id, state) {
      this.storeupdateshuipiaoVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdateshuipiao.init(id, state)
      })
    },
    storeupdateyatong(id, state) {
      this.storeupdateyatongVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdateyatong.init(id, state)
      })
    },
    storeupdaterole(id) {
      this.storeupdateroleVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdaterole.init(id)
      })
    },
    storeupdatefenzhang(id) {
      this.storeupdatefenzhangVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatefenzhang.init(id)
      })
    },
    storeupdatelianying(id) {
      this.storeupdatelianyingVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatelianying.init(id)
      })
    },
    storeupdatejifen(id) {
      this.storeupdatejifenVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatejifen.init(id)
      })
    },
    storeupdategeofence(id) {
      this.storeupdategeofenceVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdategeofence.init(id)
      })
    },
    storeupdateisphoto(id) {
      this.storeupdateisphotoVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdateisphoto.init(id)
      })
    },
    storeupdaterolearea(id) {
      this.storeupdateroleareaVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdaterolearea.init(id)
      })
    },
    storeupdatestorearea(id) {
      this.storeupdatestoreareaVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatestorearea.init(id)
      })
    },
    // 获取商户所属区域显示文本
    getStoreAreaDisplay(row) {
      const parts = []
      if (row.storeProvinceName) parts.push(row.storeProvinceName)
      if (row.storeCityName) parts.push(row.storeCityName)
      if (row.storeAreaName) parts.push(row.storeAreaName)
      return parts.join(' - ')
    },
    // 快捷设置角色
    quickSetRole(storeId, roleValue) {
      const roleNames = {
        0: '普通商户',
        1: '连锁商户总店',
        2: '城市合伙人',
        3: '区域合伙人'
      }

      this.$confirm(`确定要将该商户设置为"${roleNames[roleValue]}"吗？`, '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$post('szmcstore/update', {
          storeId: storeId,
          role1: roleValue,
          header: 'json'
        }).then((res) => {
          if (res.code === 1) {
            this.$message.success('角色设置成功')
            this.getDataList()
          } else {
            this.$message.error(res.data || '设置失败')
          }
        })
      }).catch(() => {
        // 用户取消操作
      })
    },
    storeupdatepid(id) {
      this.storeupdatepidVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatepid.init(id)
      })
    },
    storedetailchild(id) {
      this.storedetailchildVisible = true
      this.$nextTick(() => {
        this.$refs.storedetailchild.init(id)
      })
    },
    storeupdatearea(id, role) {
      this.storeupdateareaVisible = true
      this.$nextTick(() => {
        this.$refs.storeupdatearea.init(id, role)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/log/logcreatecert/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tag-color {
  height: 28px;
  border-radius: 5px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 28px;
  margin-bottom: 3px;
}

.tag-color-mini {
  height: 20px;
  border-radius: 5px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 3px;
}

.tag-color-1 {
  background: rgba(67, 202, 73, 0.1);
  border: 1px solid rgba(67, 202, 73, 1);
  color: rgba(67, 202, 73, 1);
}

.tag-color-2 {
  background: rgba(255, 75, 105, 0.1);
  border: 1px solid rgba(202, 67, 67, 1);
  color: rgba(255, 75, 105, 1);
}

.tag-color-3 {
  background: rgba(255, 171, 0, 0.1);
  border: 1px solid rgba(255, 171, 0, 1);
  color: rgba(255, 171, 0, 1);
}

.tag-color-4 {
  background: rgba(213, 0, 255, 0.1);
  border: 1px solid rgba(213, 0, 255, 1);
  color: rgba(213, 0, 255, 1);
}

.tag-color-5 {
  background: rgba(0, 175, 158, 0.1);
  border: 1px solid rgba(0, 175, 158, 1);
  color: rgba(0, 175, 158, 1);
}

.tag-color-6 {
  background: rgba(0, 156, 222, 0.1);
  border: 1px solid rgba(0, 156, 222, 1);
  color: rgba(0, 156, 222, 1);
}

.tag-color-7 {
  background: rgba(255, 96, 0, 0.1);
  border: 1px solid rgba(255, 96, 0, 1);
  color: rgba(255, 96, 0, 1);
}

.tag-color-8 {
  background: rgba(255, 110, 3, 0.1);
  border: 1px solid rgba(255, 147, 0, 1);
  color: rgba(255, 147, 0, 1);
}

.tag-color-9 {
  background: rgba(255, 0, 234, 0.1);
  border: 1px solid rgba(255, 0, 234, 1);
  color: rgba(255, 0, 234, 1);
}

.tag-color-10 {
  background: rgba(29, 38, 212, 0.1);
  border: 1px solid rgba(29, 38, 212, 1);
  color: rgba(29, 38, 212, 1);
}

.tag-color-11 {
  background: rgba(0, 175, 158, 0.1);
  border: 1px solid rgba(0, 175, 158, 1);
  color: rgba(0, 175, 158, 1);
}

.tag-color-12 {
  background: rgba(0, 156, 222, 0.1);
  border: 1px solid rgba(0, 156, 222, 1);
  color: rgba(0, 156, 222, 1);
}

.tag-color-13 {
  background: rgba(255, 96, 0, 0.1);
  border: 1px solid rgba(255, 96, 0, 1);
  color: rgba(255, 96, 0, 1);
}

.tag-color-0 {
  background: rgba(9, 186, 208, 0.1);
  border: 1px solid rgba(9, 186, 208, 1);
  color: rgba(9, 186, 208, 1);
}
.royalty-cont {
  padding: 20px 0;

  ::v-deep .has-gutter {
    .cell {
      .el-checkbox {
        display: none !important;
      }
    }
  }
}

// 角色区域列样式优化
::v-deep .el-table .cell {
  .el-button-group {
    .el-button--mini.el-button--text {
      padding: 2px 4px;
      font-size: 11px;
      margin: 0 1px;

      &:hover {
        color: #409eff;
        background-color: #ecf5ff;
      }
    }
  }
}

// 商户所属区域列样式
.store-area-display {
  font-size: 12px;
  line-height: 1.4;

  .area-text {
    color: #606266;
    margin-bottom: 2px;
  }

  .edit-btn {
    padding: 0;
    font-size: 11px;
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .warning-text {
    color: #f56c6c;

    .set-btn {
      padding: 0;
      font-size: 11px;
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

</style>