// pages/driver/mine/handOrder/handOrder.js
import util from "../../../../utils/util.js";
import config from "../../../../utils/config.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    flag: "",
    ordernumber: "",
    userid: "",
    imgUri: "https://waterstation.com.cn/szm",
    orderMainId: "",
    picurl: "",
    picurls: [], // 新增：存储多张图片的数组
    maxImageCount: 5, // 新增：最大图片数量
    order: {},
    debug: false, // 调试开关
    uploadStatus: "", // 上传状态：loading, success, error
    hasUploaded: false, // 新增：标记是否已上传新图片
    isFirstLoad: true, // 新增：标记是否是首次加载
    autoOpenCamera: true, // 新增：标记是否自动打开相机
    // 水印相关
    canvasWidth: 300,
    canvasHeight: 400,
    currentLocation: '', // 当前位置信息
    enableWatermark: true, // 是否启用水印
    fastMode: false, // 快速模式（跳过水印）
  },

  // 获取当前位置信息
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res);
        // 使用腾讯地图API进行逆地理编码（需要申请腾讯地图key）
        // 这里先使用经纬度作为位置信息，您可以根据需要集成腾讯地图API
        this.setData({
          currentLocation: `经度:${res.longitude.toFixed(6)} 纬度:${res.latitude.toFixed(6)}`
        });

        // 如果需要详细地址，可以调用腾讯地图API
        this.getAddressFromLocation(res.latitude, res.longitude);
      },
      fail: (err) => {
        console.log('获取位置失败:', err);
        this.setData({
          currentLocation: '位置获取失败'
        });
      }
    });
  },

  // 通过经纬度获取详细地址（使用后台接口）
  getAddressFromLocation(latitude, longitude) {
    // 使用后台接口进行逆地理编码
    util.ajax(config.geocodereverse, {
      latitude: latitude,
      longitude: longitude
    }, (res) => {
      if (res.data.code == 1 && res.data.data) {
        const addressData = res.data.data;

        // 根据后台返回的数据结构解析地址信息
        if (addressData.fullAddress) {
          this.setData({
            currentLocation: addressData.fullAddress
          });
        } else {
          // 如果没有完整地址，组合省市区信息
          let addressParts = [];
          if (addressData.province) addressParts.push(addressData.province);
          if (addressData.city) addressParts.push(addressData.city);
          if (addressData.district) addressParts.push(addressData.district);
          if (addressData.detailAddress) addressParts.push(addressData.detailAddress);

          this.setData({
            currentLocation: addressParts.join('') || `经度:${longitude.toFixed(6)} 纬度:${latitude.toFixed(6)}`
          });
        }
      } else {
        console.log('获取地址失败:', res.data.data || '接口返回异常');
        this.setData({
          currentLocation: `经度:${longitude.toFixed(6)} 纬度:${latitude.toFixed(6)}`
        });
      }
    }, (err) => {
      console.log('获取地址失败:', err);
      this.setData({
        currentLocation: `经度:${longitude.toFixed(6)} 纬度:${latitude.toFixed(6)}`
      });
    });
  },

  // 获取当前时间字符串
  getCurrentTimeString() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  },

  // 优化后的水印处理方法
  addWatermarkToImage(imagePath) {
    const startTime = Date.now();
    console.log('开始水印处理...', imagePath);
    console.log('当前位置信息:', this.data.currentLocation);

    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          console.log(`图片信息获取完成: ${imageInfo.width}x${imageInfo.height}`);

          // 优化：智能调整图片尺寸
          const maxSize = 1600; // 降低最大尺寸，进一步提升性能
          let targetWidth = imageInfo.width;
          let targetHeight = imageInfo.height;

          // 如果图片过大，按比例缩小
          if (imageInfo.width > maxSize || imageInfo.height > maxSize) {
            const ratio = Math.min(maxSize / imageInfo.width, maxSize / imageInfo.height);
            targetWidth = Math.floor(imageInfo.width * ratio);
            targetHeight = Math.floor(imageInfo.height * ratio);
            console.log(`图片尺寸优化: ${imageInfo.width}x${imageInfo.height} → ${targetWidth}x${targetHeight}`);
          }

          // 如果图片太小，也适当限制最小尺寸
          const minSize = 400;
          if (targetWidth < minSize && targetHeight < minSize) {
            const ratio = minSize / Math.max(targetWidth, targetHeight);
            targetWidth = Math.floor(targetWidth * ratio);
            targetHeight = Math.floor(targetHeight * ratio);
            console.log(`图片尺寸提升: ${targetWidth}x${targetHeight}`);
          }

          // 修复：需要设置canvas尺寸，但使用同步方式
          const canvasWidth = targetWidth;
          const canvasHeight = targetHeight;

          // 设置canvas尺寸（这是必需的）
          this.setData({
            canvasWidth: canvasWidth,
            canvasHeight: canvasHeight
          });

          // 等待setData完成后再创建canvas上下文
          setTimeout(() => {
            // 创建canvas上下文
            const ctx = wx.createCanvasContext('watermarkCanvas', this);

            // 检查canvas上下文是否创建成功
            if (!ctx) {
              console.error('Canvas上下文创建失败');
              reject(new Error('Canvas上下文创建失败'));
              return;
            }

            console.log('Canvas上下文创建成功');

            // 绘制原图（使用优化后的尺寸）
            console.log(`绘制原图: ${canvasWidth}x${canvasHeight}`);
            ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);

            // 优化：预计算所有水印参数
            const fontSize = Math.max(canvasWidth * 0.018, 16); // 优化字体大小计算
            const textHeight = fontSize * 1.2;
            const padding = 12;
            const backgroundHeight = textHeight * 2.2;
            const backgroundY = canvasHeight - backgroundHeight - padding;

            // 预获取文本内容
            const timeString = this.getCurrentTimeString();
            const locationString = this.data.currentLocation || '位置获取中...';

            console.log(`水印参数: fontSize=${fontSize}, backgroundY=${backgroundY}`);
            console.log(`水印文本: 时间=${timeString}, 地点=${locationString}`);

            // 优化：一次性设置所有样式，减少状态切换
            ctx.setFillStyle('#000000');
            ctx.setGlobalAlpha(0.65);
            ctx.fillRect(0, backgroundY, canvasWidth, backgroundHeight + padding);
            console.log('绘制背景完成');

            // 一次性设置文字样式
            ctx.setFillStyle('#FFFFFF');
            ctx.setGlobalAlpha(1.0);
            ctx.setFontSize(fontSize);
            ctx.setTextAlign('left');

            // 批量绘制文字
            const timeY = backgroundY + textHeight + 5;
            const locationY = timeY + textHeight;

            ctx.fillText(`时间: ${timeString}`, padding, timeY);
            ctx.fillText(`地点: ${locationString}`, padding, locationY);
            console.log('绘制文字完成');

            // 修复：确保canvas绘制完成后再转换
            ctx.draw(false, () => {
              console.log('Canvas绘制完成，开始转换图片...');
              // 增加延迟时间，确保绘制完成
              setTimeout(() => {
                console.log('开始canvasToTempFilePath...');
                wx.canvasToTempFilePath({
                  canvasId: 'watermarkCanvas',
                  x: 0,
                  y: 0,
                  width: canvasWidth,
                  height: canvasHeight,
                  destWidth: canvasWidth,
                  destHeight: canvasHeight,
                  fileType: 'jpg',
                  quality: 0.8, // 提高质量，确保水印清晰
                  success: (canvasRes) => {
                    const endTime = Date.now();
                    console.log(`水印处理完成，耗时: ${endTime - startTime}ms`);
                    console.log('生成的水印图片路径:', canvasRes.tempFilePath);
                    resolve(canvasRes.tempFilePath);
                  },
                  fail: (err) => {
                    console.error('Canvas转图片失败:', err);
                    reject(err);
                  }
                }, this);
              }, 200); // 增加等待时间，确保绘制完成
            });
          }, 100); // 等待setData完成
        },
        fail: (err) => {
          console.error('获取图片信息失败:', err);
          reject(err);
        }
      });
    });
  },

  // 快速压缩图片（不添加水印）
  compressImage(imagePath) {
    const startTime = Date.now();
    console.log('开始快速压缩图片...');

    return new Promise((resolve) => {
      wx.compressImage({
        src: imagePath,
        quality: 60, // 优化：降低压缩质量，提升速度
        success: (res) => {
          const endTime = Date.now();
          console.log(`图片压缩完成，耗时: ${endTime - startTime}ms`);
          resolve(res.tempFilePath);
        },
        fail: (err) => {
          console.error('图片压缩失败:', err);
          // 压缩失败时返回原图
          resolve(imagePath);
        }
      });
    });
  },

  // 切换水印模式
  toggleWatermark() {
    this.setData({
      enableWatermark: !this.data.enableWatermark
    });
    wx.showToast({
      title: this.data.enableWatermark ? '已启用水印' : '已禁用水印',
      icon: 'none'
    });
  },

  // 切换快速模式
  toggleFastMode() {
    this.setData({
      fastMode: !this.data.fastMode
    });
    wx.showToast({
      title: this.data.fastMode ? '快速模式已开启' : '快速模式已关闭',
      icon: 'none'
    });
  },

  // 切换调试视图
  toggleDebug() {
    this.setData({
      debug: !this.data.debug,
    });
  },

  // 切换自动拍照功能
  toggleAutoCamera(e) {
    const isEnabled = e.detail.value;
    this.setData({
      autoOpenCamera: isEnabled
    });

    // 如果开启自动拍照且当前没有图片，立即打开相机
    if (isEnabled && this.data.picurls.length === 0) {
      setTimeout(() => {
        this.autoOpenCamera();
      }, 300);
    }

    wx.showToast({
      title: isEnabled ? '已开启自动拍照' : '已关闭自动拍照',
      icon: 'none',
      duration: 1500
    });
  },

  // 预览图片
  previewImage(e) {
    // 获取当前点击的图片索引
    const index = e.currentTarget.dataset.index;
    const urls = this.data.picurls.length > 0 ? this.data.picurls : (this.data.picurl ? [this.data.picurl] : []);
    
    if (urls.length > 0) {
      wx.previewImage({
        urls: urls,
        current: urls[index || 0],
      });
    }
  },

  // 删除图片
  deletePic(e) {
    // 获取要删除的图片索引
    const index = e.currentTarget.dataset.index;
    
    wx.showModal({
      title: "提示",
      content: "确定要删除这张凭证吗？",
      success: (res) => {
        if (res.confirm) {
          // 删除数组中对应索引的图片
          let newPicurls = [...this.data.picurls];
          newPicurls.splice(index, 1);
          
          this.setData({
            picurls: newPicurls,
            picurl: newPicurls.join(','), // 将数组转为字符串保存，用逗号分隔
            uploadStatus: newPicurls.length > 0 ? "success" : "",
          });
          
          wx.showToast({
            title: "已删除",
            icon: "success",
          });
        }
      },
    });
  },

  // 自动打开相机拍照
  autoOpenCamera() {
    let that = this;

    // 检查是否还能上传图片
    const remainCount = this.data.maxImageCount - this.data.picurls.length;

    if (remainCount <= 0) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    // 直接打开相机拍照
    wx.chooseImage({
      count: 1, // 一次只拍一张
      sizeType: ["original", "compressed"],
      sourceType: ["album", "camera"], // 允许相机拍照和相册选择
      success(res) {
        console.log("自动拍照成功，获取到图片:", res.tempFilePaths[0]);
        console.log("拍照前当前图片数量:", that.data.picurls.length);

        // 更新上传状态
        that.setData({
          uploadStatus: "loading",
          autoOpenCamera: false, // 拍照后关闭自动打开标志
        });

        // 显示上传中提示
        wx.showLoading({
          title: "上传中...",
          mask: true,
        });

        // 获取临时文件路径
        const tempFilePath = res.tempFilePaths[0];

        // 处理图片（添加水印后上传）
        that.processAndUploadImage(tempFilePath);
      },
      fail(err) {
        console.log('用户取消拍照或拍照失败:', err);
        // 用户取消拍照时，关闭自动打开标志
        that.setData({
          autoOpenCamera: false
        });
      }
    });
  },

  // 处理单张图片（添加水印或快速压缩并上传）
  async processAndUploadImage(filePath) {
    let that = this;
    console.log("开始处理图片:", filePath);
    console.log("处理前当前图片数量:", that.data.picurls.length);
    console.log("当前模式 - 水印:", that.data.enableWatermark, "快速:", that.data.fastMode);

    try {
      let processedImagePath;

      if (that.data.fastMode || !that.data.enableWatermark) {
        // 快速模式：只压缩，不添加水印
        console.log("使用快速模式，跳过水印处理...");
        processedImagePath = await this.compressImage(filePath);
        console.log("快速压缩完成:", processedImagePath);
      } else {
        // 正常模式：添加水印
        console.log("使用正常模式，添加水印...");
        processedImagePath = await this.addWatermarkToImage(filePath);
        console.log("水印添加完成:", processedImagePath);
      }

      // 上传处理后的图片
      wx.uploadFile({
        url: config.uploadPicPingZheng,
        filePath: processedImagePath,
        name: "file",
        formData: {
          timestamp: new Date().getTime(), // 添加时间戳防止缓存
        },
        success(re) {
          try {
            var data = JSON.parse(re.data);
            console.log("上传返回数据:", data);

            if (data && data.data) {
              // 检查返回的数据是否已经是完整URL
              let urlss;
              if (data.data.startsWith('http')) {
                urlss = data.data;
              } else {
                urlss = that.data.imgUri + data.data;
              }
              console.log("上传成功，原始返回:", data.data);
              console.log("上传成功，最终URL:", urlss);

              // 合并已有图片和新上传的图片
              const updatedPicurls = [...that.data.picurls, urlss];
              console.log("更新前picurls:", that.data.picurls);
              console.log("更新后picurls:", updatedPicurls);

              // 隐藏loading
              wx.hideLoading();

              // 强制更新数据并触发页面重新渲染
              that.setData({
                picurls: updatedPicurls,
                picurl: updatedPicurls.join(','), // 将数组转为字符串保存，用逗号分隔
                uploadStatus: "success",
                hasUploaded: true,
              }, () => {
                // 数据更新完成后的回调
                console.log("数据更新完成，当前picurls:", that.data.picurls);
                console.log("数据更新完成，picurls长度:", that.data.picurls.length);

                // 强制触发页面重新渲染
                wx.nextTick(() => {
                  console.log("页面重新渲染完成");
                  // 再次检查数据
                  console.log("渲染后检查 - 当前picurls:", that.data.picurls);
                  console.log("渲染后检查 - picurls长度:", that.data.picurls.length);

                  // 强制刷新页面
                  that.forceRefresh();
                });
              });

              wx.showToast({
                title: "上传成功",
                icon: "success",
              });

              console.log("图片上传成功，当前图片列表:", updatedPicurls);

              // 额外的调试信息
              setTimeout(() => {
                console.log("延迟检查 - 当前picurls:", that.data.picurls);
                console.log("延迟检查 - picurls长度:", that.data.picurls.length);
              }, 1000);

            } else {
              console.error("上传返回数据异常:", data);
              wx.hideLoading();
              that.setData({
                uploadStatus: "error",
              });
              wx.showToast({
                title: "上传失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("解析返回数据失败:", error, re.data);
            wx.hideLoading();
            that.setData({
              uploadStatus: "error",
            });
            wx.showToast({
              title: "上传失败",
              icon: "none",
            });
          }
        },
        fail(error) {
          console.error("上传请求失败:", error);
          wx.hideLoading();
          that.setData({
            uploadStatus: "error",
          });
          wx.showToast({
            title: "上传失败",
            icon: "none",
          });
        },
        complete() {
          wx.hideLoading();
        }
      });
    } catch (error) {
      console.error("添加水印失败:", error);
      that.setData({
        uploadStatus: "error",
      });
      wx.showToast({
        title: "处理图片失败",
        icon: "none",
      });
      wx.hideLoading();
    }
  },

  // 调试方法：检查当前数据状态
  debugDataStatus() {
    console.log("=== 数据状态检查 ===");
    console.log("picurls:", this.data.picurls);
    console.log("picurls长度:", this.data.picurls.length);
    console.log("picurl:", this.data.picurl);
    console.log("uploadStatus:", this.data.uploadStatus);
    console.log("hasUploaded:", this.data.hasUploaded);
    console.log("==================");

    // 在真机上显示数据状态
    wx.showModal({
      title: '数据状态',
      content: `图片数量: ${this.data.picurls.length}\n上传状态: ${this.data.uploadStatus}\n已上传: ${this.data.hasUploaded}`,
      showCancel: false
    });
  },

  // 强制刷新页面数据
  forceRefresh() {
    console.log("强制刷新页面数据");
    this.setData({
      picurls: [...this.data.picurls] // 创建新数组引用，强制触发更新
    });
  },

  // 测试水印功能
  testWatermark() {
    console.log("开始测试水印功能...");

    // 创建一个测试图片
    wx.chooseImage({
      count: 1,
      sizeType: ['original'],
      sourceType: ['album'],
      success: async (res) => {
        try {
          console.log("选择测试图片成功:", res.tempFilePaths[0]);

          // 测试水印处理
          const watermarkedPath = await this.addWatermarkToImage(res.tempFilePaths[0]);
          console.log("水印测试完成:", watermarkedPath);

          // 预览水印图片
          wx.previewImage({
            urls: [watermarkedPath],
            current: watermarkedPath
          });

          wx.showToast({
            title: '水印测试完成',
            icon: 'success'
          });

        } catch (error) {
          console.error("水印测试失败:", error);
          wx.showToast({
            title: '水印测试失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.log("选择图片失败:", err);
      }
    });
  },

  // 直接使用微信原生上传接口
  changeHeadPic() {
    let that = this;
    // 计算还可以上传的图片数量
    const remainCount = this.data.maxImageCount - this.data.picurls.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount, // 最多还能选择的图片数量
      sizeType: ["original", "compressed"],
      sourceType: ["album", "camera"],
      success(res) {
        // 更新上传状态
        that.setData({
          uploadStatus: "loading",
        });

        // 显示上传中提示
        wx.showLoading({
          title: "上传中...",
          mask: true,
        });

        // 获取临时文件路径
        const tempFilePaths = res.tempFilePaths;
        const uploadPromises = [];

        // 遍历所有选择的图片，逐个处理（添加水印或快速压缩后上传）
        tempFilePaths.forEach((filePath) => {
          const uploadPromise = new Promise(async (resolve, reject) => {
            try {
              let processedImagePath;

              if (that.data.fastMode || !that.data.enableWatermark) {
                // 快速模式：只压缩，不添加水印
                processedImagePath = await that.compressImage(filePath);
              } else {
                // 正常模式：添加水印
                processedImagePath = await that.addWatermarkToImage(filePath);
              }

              // 上传处理后的图片
              wx.uploadFile({
                url: config.uploadPicPingZheng,
                filePath: processedImagePath,
                name: "file",
                formData: {
                  timestamp: new Date().getTime(), // 添加时间戳防止缓存
                },
                success(re) {
                  try {
                    var data = JSON.parse(re.data);
                    console.log("上传返回数据:", data);

                    if (data && data.data) {
                      let urlss = that.data.imgUri + data.data;
                      console.log("上传成功:", urlss);
                      resolve(urlss);
                    } else {
                      console.error("上传返回数据异常:", data);
                      reject("返回数据异常");
                    }
                  } catch (error) {
                    console.error("解析返回数据失败:", error, re.data);
                    reject("解析数据失败");
                  }
                },
                fail(error) {
                  console.error("上传请求失败:", error);
                  reject("上传失败");
                }
              });
            } catch (error) {
              console.error("添加水印失败:", error);
              reject("添加水印失败");
            }
          });
          uploadPromises.push(uploadPromise);
        });

        // 等待所有图片上传完成
        Promise.all(uploadPromises)
          .then((newUrls) => {
            // 合并已有图片和新上传的图片
            const updatedPicurls = [...that.data.picurls, ...newUrls];

            // 强制更新数据并触发页面重新渲染
            that.setData({
              picurls: updatedPicurls,
              picurl: updatedPicurls.join(','), // 将数组转为字符串保存，用逗号分隔
              uploadStatus: "success",
              hasUploaded: true,
            }, () => {
              // 数据更新完成后的回调
              console.log("批量上传完成，当前picurls:", that.data.picurls);

              // 强制触发页面重新渲染
              wx.nextTick(() => {
                console.log("批量上传页面重新渲染完成");
              });
            });

            wx.showToast({
              title: "上传成功",
              icon: "success",
            });
            
            console.log("所有图片上传成功，当前图片列表:", updatedPicurls);
          })
          .catch((error) => {
            that.setData({
              uploadStatus: "error",
            });
            
            wx.showToast({
              title: "部分图片上传失败",
              icon: "none",
            });
            
            console.error("图片上传失败:", error);
          })
          .finally(() => {
            wx.hideLoading();
          });
      },
    });
  },

  // 提交表单
  gosubmit() {
    // 提交前检查picurls是否存在
    if (this.data.picurls.length === 0) {
      util.alert("请先上传图片");
      return;
    }
    var that = this;

    // 显示加载中
    wx.showLoading({
      title: "提交中...",
      mask: true,
    });

    // 使用本地变量保存当前picurl值，避免异步问题
    const currentPicurl = this.data.picurl;

    util.ajax(
      config.updatePirurlByOrderId,
      {
        orderId: this.data.orderMainId,
        picurl: currentPicurl,
      },
      function (res) {
        wx.hideLoading();
        if (res.data.code == 1) {
          if (that.data.flag == 1) {
            util.ajax(
              config.eidtDriverState,
              {
                orderId: that.data.orderMainId,
              },
              function (res) {
                if (res.data.code == 1) {
                  setTimeout(function () {
                    if (res.data.data && res.data.data == 3) {
                      // 重置上传标志
                      that.setData({
                        hasUploaded: false,
                      });

                      util.alert("确认送达成功!", "", function () {
                        util.back();
                      });
                    } else {
                      // 没有押桶，跳到退桶回桶页面
                      wx.navigateTo({
                        url:
                          "/pages/driver/myOrder/backBucket/backBucket?showPay=1&orderNum=" +
                          that.data.ordernumber +
                          "&userId=" +
                          that.data.userid +
                          "&isuser=0" + 
                          "&bbbflag=1"
                          ,
                      });
                    }
                  }, 500);
                } else {
                  wx.showToast({
                    title: res.data.data,
                    icon: "none",
                  });
                }
              }
            );
          } else {
            // 重置上传标志
            that.setData({
              hasUploaded: false,
            });

            util.alert("保存成功!", "", function () {
              util.back();
            });
          }
        } else {
          util.alert(res.data.data);
        }
      }
    );
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (e) {
    this.setData({
      orderMainId: e.orderId,
      flag: e.flag || 0,
      ordernumber: e.ordernumber,
      userid: e.userid,
      isFirstLoad: true,
    });

    // 获取当前位置信息
    this.getCurrentLocation();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.data.isFirstLoad) {
      this.getDriverOrderInfo();
      this.setData({
        isFirstLoad: false,
      });
    } else if (!this.data.hasUploaded) {
      // 非首次加载且未上传新图片时才获取订单信息
      this.getDriverOrderInfo();
    }

    // 添加定时器检查picurl值
    if (this.data.debug) {
      this.debugInterval = setInterval(() => {
        console.log("当前picurl值:", this.data.picurl);
        console.log("当前picurls值:", this.data.picurls);
      }, 2000);
    }
  },

  getDriverOrderInfo() {
    var _this = this;
    util.ajax(
      config.getDriverOrderInfo,
      {
        orderId: this.data.orderMainId,
      },
      function (res) {
        if (res.data.code == 1) {
          var order = res.data.data;

          // 保存当前已上传的图片URL（如果存在）
          const currentPicurl = _this.data.picurl;
          const currentPicurls = _this.data.picurls.length > 0 ? _this.data.picurls : [];

          // 处理服务器返回的picurl（可能是逗号分隔的多张图片）
          let serverPicurls = [];
          if (order.picurl && !currentPicurl) {
            serverPicurls = order.picurl.split(',').filter(url => url);
          }

          _this.setData({
            order: order,
            // 只有当本地没有新上传的图片时，才使用服务器返回的picurl
            picurl: currentPicurl || order.picurl || "",
            picurls: currentPicurls.length > 0 ? currentPicurls : serverPicurls,
          });

          // 添加日志
          console.log("获取订单信息成功，当前picurl:", _this.data.picurl);
          console.log("获取订单信息成功，当前picurls:", _this.data.picurls);

          // 获取订单信息后，如果是首次加载且没有图片，自动打开相机
          if (_this.data.autoOpenCamera && _this.data.picurls.length === 0) {
            setTimeout(() => {
              _this.autoOpenCamera();
            }, 300);
          }
        } else {
          util.showText(res.data.data);
          _this.setData({
            order: "",
          });
        }
      }
    );
  },

  // 其他生命周期函数保持不变...
  onReady: function () {},
  onHide: function () {
    // 清除定时器
    if (this.debugInterval) {
      clearInterval(this.debugInterval);
    }
  },
  onUnload: function () {
    // 清除定时器
    if (this.debugInterval) {
      clearInterval(this.debugInterval);
    }
  },
  onPullDownRefresh: function () {},
  onReachBottom: function () {},
  onShareAppMessage: function () {},

  // 保留原有的idcardChange方法，以防其他地方调用
  idcardChange(e) {
    console.log("上传", e);
    this.setData({
      picurl:
        e.detail.value.length > 0 ? this.data.imgUri + e.detail.value[0] : "",
    });
  },
});
