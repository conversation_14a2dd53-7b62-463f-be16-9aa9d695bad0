import variable from "./variable.js";
var host = variable.host;
// var host2 = variable.host2;

var config = {
  /*
   ** 派单员接口 Start
   */
  // 2019-05-15
  // 客户押桶
  updatedeliveryinfo: `${host}/szmb/newdebtbuckcontroller/updatedeliveryinfo`,
  // 获取图形验证码
  getImgCode: `${host}/szmb/code/getImgCode`,
  //   押桶记录
  selectpledglist: `${host}/szmb/newdebtbuckcontroller/selectpledglist`,
  // 补押桶提交
  bucketpay: `${host}/szmc/pledgebuckorderpaycontroller/bucketpay`,
  // 派单员补押桶列表接口 老
  // selectdebtdeatil: `${host}/szmb/newdebtbuckcontroller/selectdebtdeatil`,
  // 派单员补押桶列表接口 新
  selectdebtdeatil: `${host}/szmb/newdebtbuckcontroller/selectproductlistall1`,
  // 设置商品优惠价格和水票买赠规则
  updatediscount: `${host}/szmb/user/updatediscount`,

  //
  getNowShopCard: `${host}/api/excal/selectdeliverycode`,
  // 派单员新增订单 POST
  insertdeliveryorder: `${host}/szmb/deliveryordercontroller/insertdeliveryorder`,

  // 查看临时派单员接单状态
  getDeliveryOrderState: `${host}/szmb/szmsendmembercontroller/updatedeliveryorder`,

  // 查询某个帮助类别下的帮助中心
  getHelpAll: `${host}/szmb/helpcontroller/selectall`,
  // 删除帮助POST
  delHelpItem: `${host}/szmb/helpcontroller/delitem`,
  // 新增自定义帮助POST
  addStoreHelp: `${host}/szmb/helpcontroller/addnewitem`,
  // 模板新增到商户模板下POST
  addHelpToStore: `${host}/szmb/helpcontroller/additem`,
  // 修改帮助POST
  editHelpItem: `${host}/szmb/helpcontroller/updateitem`,


  // 订单管理 查询全部订单
  allOrderList: `${host}/szmb/szmborder/allorder`,
  //
  deliveryquern: `${host}/szmb/szmstoreapplycontroller/deliveryquern`,
  deliveryquernback: `${host}/szmb/newdebtbuckcontroller/back`,
  // 物资确认送达
  deliveryconfirm: `${host}/szmb/szmstoreapplycontroller/deliveryconfirm`,
  // 获取待处理信息
  pendingitem: `${host}/szmb/szmbstorecontroller/pendingitem`,
  // 获取语音
  deliveryvoiceremind: `${host}/szmb/szmbstorecontroller/deliveryvoiceremind`,
  // 修改待处理信息
  readpendingitem: `${host}/szmb/szmbstorecontroller/readpendingitem`,
  // 获取退还管理角标
  selectrecorddedeliveryidcornermark: `${host}/szmb/szmstoreapplycontroller/selectrecorddedeliveryidcornermark`,
  // 查询自送开关状态
  selectstoreissend: `${host}/szmb/szmbvippricecontroller/selectstoreissend`,
  // 自送开关
  updatestoreisseng: `${host}/szmb/szmbvippricecontroller/updatestoreisseng`,
  // 派单员全部订单
  getDeliveryOrder: `${host}/szmb/szmsendmembercontroller/selectall`,

  // 获取商户信息
  getStoreInfo: `${host}/szmb/szmbstorecontroller/selectstoreid`,
  // 设置商户选择的短信模块 POST
  setStoreSms: `${host}/szmb/szmbsms/setSms`,
  // 获取所有短信模块 POST
  getAllSms: `${host}/szmb/szmbsms/selSms`,

  // 查看派单员回收订单记录 POST /szmb/szmstoreapplycontroller/selectdelivery
  getRecoveryOrder: `${host}/szmb/szmstoreapplycontroller/selectdelivery`,
  // 派单员查看指派记录
  selectdedeliveryid: `${host}/szmb/szmstoreapplycontroller/selectdedeliveryid`,
  // 派单员汇总角标
  selectdeliverycollect: `${host}/szmb/szmsendmembercontroller/selectdeliverycollect`,

  // 查看用户的提成模板 POST
  selectdeliveryrule: `${host}/szmb/szmsendmembercontroller/selectdeliveryrule`,

  // 商户审核派单员新增用户 POST
  storeCheckuser: `${host}/szmb/deliveryusercontroller/checkuser`,
  // 商户查看派单员新增用户类别 POST
  selectbyduser: `${host}/szmb/deliveryusercontroller/selectbyduser`,
  //根据用户id查询信息
  selectbyuservo: `${host}/szmcuercontroller/selectbyuservo`,
  // 派单员新增用户 POST
  deliveryAdduser: `${host}/szmb/deliveryusercontroller/adduser`,
  // 派单员查看用户 POST
  findUserList: `${host}/szmb/deliveryusercontroller/selectalluser`,
  // 派单员修改用户 POST
  deliveryEditUser: `${host}/szmb/deliveryusercontroller/updateuser`,

  // 商家审核派单员提交的添加用户的申请 POST
  checkDeliveryAdduser: `${host}/szmb/deliveryusercontroller/checkuser`,

  // 年度收入对比
  incomeComparison: `${host}/szmb/totalassets/selectbytwoyeartotal`,
  // 派单员模糊查询 POST
  driverLike: `${host}/szmb/deliveryinfo/selectdelivery`,
  // 修改派单员状态 POST
  eidtDriverState: `${host}/szmb/deliveryinfo/updatdelivery`,

  // 固定派单员接单 POST
  fixedDeliveryOrder: `${host}/szmb/storesendpaycontroller/insertsenddelivery`,
  // 指派派单员 POST
  // selectdelivery: `${host}/szmb/szmsendmembercontroller/insertdeliverydeduct`,
  // 指派派单员 郝震 POST
  selectdelivery: `${host}/szmb/szmborder/selectdeliveryid`,
  // 重新指派
  selectdeliveryidagain: `${host}/szmb/szmborder/selectdeliveryidagain`,

  // 查看所有店铺的 派单员 POST
  getShopAllDelivery: `${host}/szmb/szmsendmembercontroller/selectdelectstate`,

  // 派单员端-线下订单查看详情接口
  downLineOrderDetail: `${host}/szmb/szmsendmembercontroller/selectlineorderdeatil`,
  //派单员端-线下订单接单
  acceptLineDownOrder: `${host}/szmb/szmsendmembercontroller/selectlinefifm`,

  //派单员-提成-老接口-
  getNewCashInfo: `${host}/szmb/szmsendmembercontroller/selectdeliverydeduct`,

  //派单员-已接单-线下-确认送达
  sureLineDownOrder: `${host}/szmb/szmsendmembercontroller/selectlinefifm`,


  // 登录接口 POST
  driverLogin: `${host}/szmb/deliveryusercontroller/login`,
  // 绑定配送员openId POST
  driverBind: `${host}/szmb/deliveryusercontroller/bindphone`,
  driverInfo: `${host}/szmb/deliveryusercontroller/info`,

  // 查看所有可接的单 POST
  getAllOrder: `${host}/szmb/szmsendmembercontroller/selectuseridall`,
  // 接单统计
  selectuseridallcount: `${host}/szmb/szmsendmembercontroller/selectuseridallcount`,
  // 查看已经接受全部信息 POST POST
  getAlreadyOrder: `${host}/szmb/szmsendmembercontroller/selectsendmemberbyuserid`,
  // 查看提现信息 POST
  getCashInfo: `${host}/szmb/szmsendmembercontroller/selectfloormoney`,
  // 查看订单的详细信息 POST
  getDriverOrderInfo: `${host}/szmb/szmborder/selectorderone`,
  selectorderonebyordernum: `${host}/szmb/szmborder/selectorderonebyordernum`,
  //查看订单详情-货到付款-确认收款
  makeSureMoney: `${host}/szmb/szmsendmembercontroller/deliveryaffi`,
  // 缴纳保证金
  payMoney: `${host}/szmb/szmsendmembercontroller/sendPayMoney`,
  /*
   ** 派单员接口 End
   */

  /*
   ** 王肖飞
   ** Start
   */
  // 查看店铺的库存和上下架数量 POST POST
  getStoreStock: `${host}/szmb/szmbhomepagecontroller/selectstorestockall`,
  // 首页 年月日 POST
  getYearClassify: `${host}/szmb/szmbhomepagecontroller/selectorderyeat`,
  // 首页 已完成未完成 POST
  getYesAndNo: `${host}/szmb/szmbhomepagecontroller/selectsoderdone`,
  // 首页 昨天 今天 POST
  getYTClassify: `${host}/szmb/szmbhomepagecontroller/selectstoretodyanduptody`,

  // 首页 普通和VIP POST
  getVipAndCom: `${host}/szmb/szmbhomepagecontroller/selectstorebyvip`,
  // getVipAndCom: `${host}/szmb/szmbhomepagecontroller/selectuserviplist`,

  // 新增帮助中心问题 POST
  addHelpContent: `${host}/szmb/szmbhelpcontercontroller/inserthelpconter`,
  // 查看帮助中心 POST
  seeHelpContent: `${host}/szmb/szmbhelpcontercontroller/selecthelpconter`,
  // 删除标题和内容 POST
  delHelpContent: `${host}/szmb/szmbhelpcontercontroller/delecthelpconter`,

  // 年度总资产 POST
  yearTotalAssets: `${host}/szmb/totalassets/selectbyoneyear`,
  // 年度总资产对比 POST
  yearTotalAssetsContrast: `${host}/szmb/totalassets/selectbytwoyear`,

  // 派单员 POST
  deliveryBoy: `${host}/szmb/szmbstorefroomandaccontcontroller/selectsendmessage`,
  // 往来账目 POST
  runningAccount: `${host}/szmb/szmbstorefroomandaccontcontroller/selectdebtandbuck`,

  //修改订单状态 POST
  updateOrderState: `${host}/szmb/szmborder/updatestate`,
  // 已完成订单 POST
  getOverOrderList: `${host}/szmb/szmborder/selectfinish`,
  // 订单详情 POST
  getOrderInfo: `${host}/szmb/szmborder/selectorderone`,
  // 根据状态查看订单 POST
  getOrderByState: `${host}/szmb/szmborder/selectstete`,
  // 待处理订单 POST
  getOrderingList: `${host}/szmb/szmborder/selectuntreated`,


  // 欠款明细 POST
  getFundInfo: `${host}/szmb/fund/selectall`,
  // 欠款总额 POST
  getFund: `${host}/szmb/fund/selectsum`,
  // 获取商家收入 POST
  getShopIncomeInfo: `${host}/szmb/szmstorebillcontroller/selectStorebilldeatil`,
  // 获取商家收入明细 POST
  getShopIncome: `${host}/szmb/szmstorebillcontroller/selectstorebillconutall`,
  // 获取库存明细 POST
  getInventoryInfo: `${host}/szmb/inventory/selectall`,
  // 获取库存总数 POST
  getInventory: `${host}/szmb/inventory/selectsum`,
  // 获取钱包信息 POST
  getWalletBalance: `${host}/szmb/szmbstorewallet/selectbyid`,
  // 获取钱包明细 POST
  getWalletInfo: `${host}/szmb/szmbstorewallet/select`,
  // 钱包提现 POST
  walletExtract: `${host}/szmb/szmbstorewallet/insert`,
  // 提现申请记录
  walletExtractRecord: `${host}/szmb/szmbstorewallet/outlist`,

  /*
   ** 王肖飞
   ** End
   */
  // 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、

  //发送验证码
  sendCode: `${host}/szmcuercontroller/sendcode`,
  //商家注册
  shopRegister: `${host}/szmb/szmbstorecontroller/register`,
  //上传图片
  uploadImgs: `${host}/uploadcontroller/upload`,
  //微信绑定storeId
  bindStoreId: `${host}/szmb/szmbstorecontroller/storeidbindwx`,
  //完善商户信息、
  perfectStore: `${host}/szmb/storeapplyfor/update`,
  //查询商户信息
  lookUpStoreInfo: `${host}/szmb/storeapplyfor/selectbyid`,
  //商家登录接口
  shopLogin: `${host}/szmb/szmbstorecontroller/storelogin`,
  //分类筛选-查询 - 旧
  lookUpClassify: `${host}/szmb/productclassifymastercontroller/addproductclassify`,

  //分类筛选-查询-新
  lookUpClassify1: `${host}/szmb/productclassifyandbrandcontroller/selectallclassandbrand`,

  //分类筛选-更新
  updateClassify: `${host}/szmb/productclassifymastercontroller/updateproductclassify`,
  //分类筛选-取消勾选
  cancelClassify: `${host}/szmb/productclassifymastercontroller/selectbyclassifyid`,
  //分类筛选-新增分类
  addClassify: `${host}/szmb/szmbserviceset/setclass`,
  //分类删选-删除分类
  delClassify: `${host}/szmb/szmbserviceset/delClass`,
  //分类编辑-查询
  lookUpMineClassify: `${host}/szmb/szmbserviceset/selclass`,

  //商品管理-页面查询
  lookUpShopAdmin: `${host}/szmb/szmstoreshopcontroller/selectallshop`,
  //商品管理-页面查询-另外一个
  lookUpShopAdmin1: `${host}/szmb/szmstoreshopcontroller/selectallstore`,
  //商品管理-上架与下架
  shelfState: `${host}/szmb/szmstoreshopcontroller/updateshopstate`,
  //商品管理-推荐与取消推荐
  recommend: `${host}/szmb/szmstoreshopcontroller/updateshoprecommend`,
  //商品管理-品牌分类
  brandClassify: `${host}/szmb/productclassifyandbrandcontroller/selectbyproductclassidandclassid`,
  //商品管理-添加品牌
  addBrandClassify: `${host}/szmb/szmbrandcontroller/insertbrand`,
  //商品管理-品牌关键字查找
  brandkey: `${host}/szmb/productclassifyandbrandcontroller/findbyname`,
  //商品管理-品牌分类提交
  submitBrand: `${host}/szmb/productclassifyandbrandcontroller/addbrands`,
  //商品管理-品牌分类编辑
  editBrand: `${host}/szmb/szmbrandcontroller/updatebrandname`,
  //商品管理-品牌分类删除
  delBrand: `${host}/szmb/szmbrandcontroller/delectbrand`,



  // 不用
  //增加商品-规格-vip价格查询
  lookUpVipPrice: `${host}/szmb/szmbvippricecontroller/selectvipprice`,
  //增加商品-规格-vip价格增加
  addVipPrice: `${host}/szmb/szmbvippricecontroller/inservipprice`,
  //增加商品-规格-vip编辑更新
  editVipPrice: `${host}/szmb/szmbvippricecontroller/updatevipprice`,
  //结束

  //增加商品-规格-vip等级
  lookUpVipLevel: `${host}/szmb/vipvontroller/selectvipall`,
  //新增商品-服务
  addService: `${host}/szmb/szmshopservicecontroller/insertservice`,
  //新增普通商品
  addGoods: `${host}/szmb/szmstoreshopcontroller/insertstoreshop`,
  //编辑普通商品
  editGoods: `${host}/szmb/szmstoreshopcontroller/selectspu`,
  //规格详情查看
  lookUpSpec: `${host}/szmb/szmstoreshopcontroller/selectsku`,
  //查看是否设置vip
  lookSetVip: `${host}/szmb/szmstoreshopcontroller/selectstorevipall`,

  // 我的-用户管理-查询用户信息查询
  lookUpUser: `${host}/szmb/user/selectuser`,
  // 我的-用户管理-新增用户
  addUser: `${host}/szmb/user/insertuser`,
  //我的-用户管理-查看用户详情
  lookUpUserDetail: `${host}/szmb/user/selectone`,
  //我的-用户管理-编辑用户信息
  editUser: `${host}/szmb/user/updatetuser`,
  //vip等级查询
  getVip: `${host}/szmb/vipvontroller/selectvipall`,
  //vip商品列表查询、
  lookUpVipGoods: `${host}/szmb/szmstoreshopcontroller/selectskulist`,
  //查询商品的VIP等级和VIP价格
  lookUpGoodsVip: `${host}/szmb/szmbvippricecontroller/selectskuviplist`,
  //添加商品的VIP等级价格
  addGoodsVip: `${host}/szmb/szmbvippricecontroller/insertskuandvip`,




  //派单管理-派单员管理-查询派单员
  lookUpDriver: `${host}/szmb/szmsendmembercontroller/selectsendmemberbystoreid`,
  //派单管理-派单员管理-新增派单员
  addDriver: `${host}/szmb/szmsendmembercontroller/insertsendmember`,
  //派单管理-派单员管理-查看派单员详细信息
  lookUpDriverDetail: `${host}/szmb/szmsendmembercontroller/selectbystoreanddelivery`,
  //派单员管理-派单员详情-派单员扣除
  takeOffDriverMoney: `${host}/szmb/storesendpaycontroller/deductsengmoneyall`,
  //派单员管理-派单员详情-派单员退还
  returnDriverMoney: `${host}/szmb/storesendpaycontroller/storepaydelivery`,



  //派单管理-派单员管理-编辑派单员
  editDriver: `${host}/szmb/szmsendmembercontroller/updatesendmeber`,
  //派单管理-派单员管理-楼层提成查看
  lookUpFloor: `${host}/szmb/szmsendmembercontroller/selectstorefloor`,
  //派单管理-派单员管理-楼层提成编辑
  editFloor: `${host}//szmb/szmsendmembercontroller/updatefloor`,

  //售后管理-查看售后列表
  lookUpSaleList: `${host}/szmb/orderreturn/select`,
  //售后管理-处理售后状态
  dealSaleList: `${host}/szmb/orderreturn/update`,
  //售后管理-退换货详情
  saleGoodsDetail: `${host}/szmb/orderreturn/selectlike`,

  //中心图片库 POST /szmb/imagescontroller/selectalllevel
  // centerImgs: `${host}:10000/szmb/imagescontroller/selectalllevel`,
  centerImgs: `${host}/szmb/imagescontroller/selectalllevel`,

  //套餐申请查询
  lookUpMeal: `${host}/szmb/szmstoreapplycontroller/selectstoreapply`,
  //套餐申请详细信息查询
  lookUpMealDetail: `${host}/szmb/szmstoreapplycontroller/selectapplydeatil`,
  //处理套餐申请
  dealMealApply: `${host}/szmb/szmstoreapplycontroller/updateapplystate`,

  //组合套餐列表
  groupList: `${host}/szmb/szmshopgroupcontroller/selectshopgrouplistall`,
  //修改套餐的上下架
  doUpGroup: `${host}/szmb/szmshopgroupcontroller/updateshopgroupstate`,
  //组合套餐的列表
  lookUpGroupList: `${host}/szmb/szmshopgroupcontroller/selectskulistall`,
  //组合套餐列表查询关键字
  selectshopgroupname: `${host}/szmb/szmshopgroupcontroller/selectshopgroupname`,
  //添加套餐
  addMeal: `${host}/szmb/szmshopgroupcontroller/insertshopgroup`,
  //编辑套餐
  editMeal: `${host}/szmb/szmshopgroupcontroller/selectshopgroupdeatil`,
  //买赠查询列表
  lookUpSendGoods: `${host}/szmb/szmstoreshopcontroller/selectskulistall`,
  //修改买赠优惠
  changeSendGoods: `${host}/szmb/szmstoreshopcontroller/updateskustate`,


  //积分商城-兑换商品
  lookUpjifenGoods: `${host}/szmb/szmbstoreintegralcontroller/selectstoresku`,
  //积分商城-兑换商品-开启状态
  dealjifenGoods: `${host}/szmb/szmbstoreintegralcontroller/updateintegralsate`,
  //积分商城-兑换水票
  lookUpjifenTicket: `${host}/szmb/szmbstoreintegralcontroller/selectstorewter`,
  //积分商城-兑换税票-状态修改
  dealjifenTicket: `${host}/szmb/szmbstoreintegralcontroller/updatewaterstate`,
  //积分商城-兑换商品-修改积分
  editJifenGoods: `${host}/szmb/szmbstoreintegralcontroller/updateinteferanumber`,
  //积分商城-兑换水票-修改积分
  editJifenTicket: `${host}/szmb/szmbstoreintegralcontroller/updatestorewater`,
  //积分商城-兑换明细-获取列表
  lookUpExchangeList: `${host}/szmb/smzbintegralordercontroller/findallbystoreid`,
  //积分商城-兑换明细-发货
  sendGoods: `${host}/szmb/smzbintegralordercontroller/updateorder`,

  //售后评价-售后评价
  afterSale: `${host}/szmb/orderevaluate/selectafter`,
  //售后评价-商品评价
  afterSaleGoods: `${host}/szmb/orderevaluate/selectbystoreid`,

  //消息管理-选择用户
  selectPeopleList: `${host}/szmb/msg/selectuser`,
  //消息管理-选择用户-关键字搜索
  selectPeople: `${host}/szmb/msg/selectlike`,
  //消息管理-添加消息
  addTidings: `${host}/szmb/msg/insert`,

  //订单设置-查询
  lookUpOrderSet: `${host}/szmb/szmbserviceset/select`,
  //订单设置-保存
  saveOrderSet: `${host}/szmb/szmbserviceset/update`,
  //vip设置-查询
  lookUpVip: `${host}/szmb/vipvontroller/selectvipall`,
  //vip设置-添加
  addVip: `${host}/szmb/vipvontroller/insertvip`,
  //vip设置-修改
  editVip: `${host}/szmb/vipvontroller/updatevipname`,
  //充值设置-查询
  lookUpRecharge: `${host}/szmb/storedeatilcontroller/selectstorerule`,
  //冲值设置-保存
  saveRecharge: `${host}/szmb/storedeatilcontroller/insertstorerule`,

  //安全设置-查询商家账号密码
  lookUpStoreAccount: `${host}/szmb/storedeatilcontroller/selectstoredeatil`,
  //安全设置-修改手机号
  changePhone: `${host}/szmb/updatephone/update`,
  //安全设置-修改密码
  changePassword: `${host}/szmb/storedeatilcontroller/updatestorepassword`,
  //配送规则-查询
  lookUpSendRule: `${host}/szmb/szmshoptitlecontroller/selectstoreshipfig`,
  //配送规则-修改
  changeSendRule: `${host}/szmb/szmshoptitlecontroller/insertstoreshipfig`,
  //广告管理-查询
  lookUpAD: `${host}/szmb/imagescontroller/selectallhome`,
  //广告管理-保存
  saveAD: `${host}/szmb/imagescontroller/updatehome`,
  //头条管理-查询
  lookUpTitle: `${host}/szmb/szmshoptitlecontroller/selecttitle`,
  //头条管理-新增
  addTitle: `${host}/szmb/szmshoptitlecontroller/inserttitle`,
  //头条管理-编辑
  editTitle: `${host}/szmb/szmshoptitlecontroller/selectnews`,
  //头条管理-删除
  delTitle: `${host}/szmb/szmshoptitlecontroller/delectstoretitle`,

  //配送说明-查询记录
  lookUpDriverList: `${host}/szmb/deliveryinfo/select`,
  //配送说明-添加
  addDriverList: `${host}/szmb/deliveryinfo/insertinfo`,
  //配送说明-删除
  deleteDriverList: `${host}/szmb/deliveryinfo/updatstate`,
  //桶押金设置-查询
  lookUpSetBucketMoney: `${host}/szmb/szmbserviceset/selbucket`,
  //桶押金设置
  setBucketMoney: `${host}/szmb/szmbserviceset/setbucket`,
  //保证金查询
  lookUpPromiseMoney: `${host}/szmb/szmbserviceset/selensure`,
  //保证金添加
  addPromiseMoney: `${host}/szmb/szmbserviceset/updateensure`,

  //资产管理-欠款用户信息
  assetsPeple: `${host}/szmb/szmbuserandstorebillcontroller/selectstoreuserall`,
  //资产管理-欠款用户信息详情
  assetsPeopleDetail: `${host}/szmb/szmbuserandstorebillcontroller/selectstoreuserdeatil`,
  //资产管理-扣除用户桶押金
  takeOffBucket: `${host}/szmb/storesendpaycontroller/deductUserBuckMoneyAll`,
  //资产管理-查看派单员详细信息-确认还款，还桶
  sureDriverOrderList: `${host}/szmb/szmbuserandstorebillcontroller/storeaffrom`,

  //水票管理-查看商家水票信息
  lookUpWaterTicket: `${host}/szmb/szmshopwatercontroller/selctshopwater`,
  //水票管理-查看商家商品
  lookUpStoreGoods: `${host}/szmb/szmshopwatercontroller/selectskustore`,
  //税票管理-添加水票
  addWaterTicket: `${host}/szmb/szmshopwatercontroller/inserwater`,
  //水票管理-编辑库存
  editWaterTicketStock: `${host}/szmb/szmshopwatercontroller/updatewater`,

  //服务订单-查询服务列表
  lookUpServiceList: `${host}/szmb/serviceorder/selectall`,
  //服务订单-查询服务评价
  lookUpServiceComment: `${host}/szmb/serviceorder/selectbystoreId`,
  //服务订单-处理服务订单
  dealServiceList: `${host}/szmb/serviceorder/update`,
  //服务订单-订单详情
  serviceOrderDetail: `${host}/szmb/serviceorder/selectone`,

  //正式版-查询
  lookUpMoney: `${host}/szmb/rule/selectstoreid`,
  //正式版-充值
  toRecharge: `${host}/api/rechargeable/wxPay-B`,

  //我的-试营业倒计时
  meTimeOut: `${host}/szmb/storeapplyfor/selectbystoreid`,
  //我的-店铺信息
  lookUpStore: `${host}/szmb/storeapplyfor/selectbyid`,

  //全局新订单查询
  lookUpNewOrder: `${host}/szmb/szmborder/selectremind`,

  //我的-服务订单数量查询  暂时不用
  lookUpMineServiceNum: `${host}/szmb/serviceorder/count`,
  //我的-红点数量查询
  lookUpMineRedNum: `${host}/szmb/szmbvippricecontroller/selectstorenumber`,

  //app-记录登录状态
  saveLoginState: `${host}/szmb/szmbstorecontroller/storeredis`,
  //app-清除登录状态
  removeLoginState: `${host}/szmb/szmbstorecontroller/exit`,

  //手填订单-添加
  addHandOrder: `${host}/szmb/szmborderstorecontroller/addorder`,
  //手填订单列表查询
  lookUpHandOrder: `${host}/szmb/szmborderstorecontroller/selectorderbystoreid`,
  //查看手填订单详情
  lookUpHandOrderDetail: `${host}/szmb/szmborderstorecontroller/selectbyordernum`,
  //修改手填订单
  changeHandOrder: `${host}/szmb/szmborderstorecontroller/updateorder`,

  //短信管理页面
  messageAdmin: `${host}/szmb/szmbsms/select`,
  //短信充值页面展示
  showMessage: `${host}/szmb/szmbsms/selectprice`,
  //短信充值
  rechargeMessage: `${host}/api/rechargeable/wxPay-Sms`,


  //查看固定派单员
  lookOfficialWorker: `${host}/szmb/deliveryusercontroller/selectsenddeletestate`,
  //绑定固定派单员
  bindOfficialWorker: `${host}/szmb/deliveryusercontroller/bingduser`,
  //关键字搜索固定派单员
  toSearchOfficialWorker: `${host}/szmb/deliveryusercontroller/selectdeliveryusername`,

  //查询借物管理
  lookUpBorrowBucket: `${host}/szmb/szmbserviceset/bandrlist`,
  //借物管理-同意拒绝
  checkBorrowBucket: `${host}/szmb/szmbserviceset/borrow`,
  //还物管理-同意拒绝
  checkReturnBucket: `${host}/szmb/szmbserviceset/repay`,

  //还桶/还物-指派
  returnBindWorker: `${host}/szmb/szmstoreapplycontroller/updatedesinage`,
  //还东西-查看固定派单员
  lookUpReturnOfficialPeople: `${host}/szmb/szmsendmembercontroller/selectdelectstate`,
  //还东西-搜索固定派单员
  toSearchReturnOfficialPeople: `${host}/szmb/szmsendmembercontroller/selectdeliveryname`,

  //新增商品-查询商品分类
  lookUpAddGoodsClassify: `${host}/szmb/szmbrandcontroller/selectallclassandbrand`,
  //新增商品-查询商品分类下的品牌
  lookUpAddGoodsBrands: `${host}/szmb/szmbrandcontroller/selectallbrand`,

  //资产管理-汇总-借物未还详情
  lookUpCollectInfo: `${host}/szmb/borrowcontroller/selectallbyuserid`,
  //资产管理-汇总-压桶未退，欠款未还详情
  lookUpCollectReturnBucketInfo: `${host}/szmb/szmbuserandstorebillcontroller/selectstorealllist`,

  //用户绑定优惠价格
  bindDiscount: `${host}/szmb/szmbvippricecontroller/selectdiscounts`,
  //查询用户绑定优惠
  lookUpBindDiscount: `${host}/szmb/szmbvippricecontroller/selectuserproductbydeliveryid`,

  //借物添加备注
  borrowAddRemarks: `${host}/szmb/szmstoreapplycontroller/updateremark`,

  //新增客户-查询权限
  lookAddUserPower: `${host}/szmb/user/selectdeliverypower`,





  // 2019-06-10 产品库
  //查看全部分类下的全部商品
  lookUpAllProducts: `${host}/szmb/newinsertproductcontroller/selectstoreproduct`,
  // 全部分类查询
  lookUpAllClassify: `${host}/szmb/newclasscontroller/selectclass`,
  //产品库上架
  upShelfProduct: `${host}/szmb/newinsertproductcontroller/insertproductlist`,
  //产品库新增商品
  addProductLibrary: `${host}/szmb/szmstoreshopcontroller/insertstoreshop`,
  //产品库搜索商品
  searchProductLibrary: `${host}/szmb/newclasscontroller/selectproductall`,

  //库存预警-查询分类列表
  lookUpStoreClassify: `${host}/szmb/productclasscontroller/selectclass`,
  //修改库存预警值
  changeStoreWarning: `${host}/szmb/productclasscontroller/updateclassnumber`,
  //删除组合商品
  delGroupGoods: `${host}/szmb/szmshopgroupcontroller/delectshopgroup`,
  //客户详情
  lookUpCostomerDetail: `${host}/szmb/productclasscontroller/selectuserdeatil`,
  //编辑水票查询详细信息
  lookUpWaterDetailInfo: `${host}/szmb/szmshopwatercontroller/selectwater`,
  //编辑水票信息
  changeWaterInfo: `${host}/szmb/szmshopwatercontroller/updatewaterall`,


  //派单员提成
  deliverMoneyDetail: `${host}/szmb/szmsendmembercontroller/selectdeliverydetail`,
  selectdeliverydetailanalysis: `${host}/szmb/szmsendmembercontroller/selectdeliverydetailanalysis`,
  selectdeliverydetailproduct: `${host}/szmb/szmsendmembercontroller/selectdeliverydetailproduct`,
  //派单员结算
  accountDelivery: `${host}/szmb/deliveryusercontroller/clearmoney`,


  // 2019-09-09 回桶管理
  // 查询欠桶品牌
  // lookUpAllBucketName: `${host}/api/debtbuckcontroller/selectbrand`,
  // 查询欠桶品牌
  lookUpAllBucketName: `${host}/szmb/newdebtbuckcontroller/selectuserdebtlist`,
  //查询所有桶品牌
  lookUpAllBrand: `${host}/api/debtbuckcontroller/selectstorebrandlist`,
  // 提交回桶单
  // submitBucketOrder: `${host}/api/debtbuckcontroller/insertrepaybuck`,
  submitBucketOrder: `${host}/szmb/newdebtbuckcontroller/insertrepaybuck`,
  // 查看回桶单
  lookUpbackBucketOrder: `${host}/szmb/newdebtbuckcontroller/selectuserrepaylist`,
  selectuserrepaysum: `${host}/szmb/newdebtbuckcontroller/selectuserrepaysum`,


  //清除回桶单
  clearBackOrder: `${host}/api/debtbuckcontroller/delectordernumber`,


  //上传头像
  upLoadHeadPic: `${host}/szmb/szmsendmembercontroller/insertdeliveryimg`,

  // 我的-客户管理
  lookUpMyCustomer: `${host}/szmb/deliveryordercontroller/selectbydeliveryuserlist`,
  //我的-线下订单查询
  lookUpMyOrder: `${host}/szmb/deliveryordercontroller/selectdeliveryorderlist`,
  //我的-线下订单详情
  lookUpMyOrderDetail: `${host}/szmb/deliveryordercontroller/selectdeliveryorderdeatil`,
  //我的-线下订单-押桶
  orderToPledBucket: `${host}/szmb/deliveryordercontroller/selectBrandMoney`,
  //我的-线下订单-修改订单状态
  orderToChangeStatus: `${host}/szmb/deliveryordercontroller/updatedeliveryorder`,
  // 订单-更新用户基本信息
  updateuserinfo: `${host}/szmb/deliveryordercontroller/updateuserinfo`,
  // 我的-线下订单-押桶提交
  orderToPledBucketSubmit: `${host}/szmb/deliveryordercontroller/insertdeliverypledgbuck`,
  //我的=线下订单-回桶
  orderToBackBucketList: `${host}/szmb/deliveryordercontroller/selectuserrepaylistbrand`,
  //我的-线下订单-回桶提交
  orderToBackBucketSubmit: `${host}/szmb/deliveryordercontroller/adduserrepay`,
  //我的-线下订单-回桶订单删除
  orderToBackBucketDelete: `${host}/szmb/deliveryordercontroller/delectdebtrepay`,
  //我的-线下订单-退货
  orderToReturnGoods: `${host}/szmb/deliveryordercontroller/updatedeliveryorder`,
  //我的-选择商品-用户优惠商品查询
  lookUpUserGroupList: `${host}/szmb/deliveryordercontroller/selectuserdiscountlist`,
  //我的-客户管理-个人信息
  customerToUserDetail: `${host}/szmb/deliveryordercontroller/selectreleveuserdeatil`,
  //我的-客户管理-桶详情-押桶管理
  customerToBucketDetail: `${host}/szmb/deliveryordercontroller/selectuserpledglist`,
  //我的-客户管理-桶详情-退桶管理
  customerToReturnBucket: `${host}/szmb/deliveryordercontroller/selectuserbackbucklist`,
  //我的-客户管理-桶详情-回桶管理
  customerToBackBucket: `${host}/szmb/deliveryordercontroller/selectuserrepaylist`,
  //我的-客户管理-通详情-退桶提交
  customerToReturnBucketSubmit: `${host}/szmb/deliveryordercontroller/insertdeliverybackbuck`,

  //客户管理-编辑-月付开关
  yfSwitch: `${host}/szmb/user/paymonthlyopen`,

  //地址查询
  lookUpAddress: `${host}/dpt/address/business`,

  //生成店铺名片
  getMyCard: `${host}/api/excal/selecttoke`,

  /**
   * start yanxingwang 2020-2-17
   */
  // 查询空桶信息
  selectbucketbycardid: `${host}/bucketcontroller/selectbucketbycardid`,
  // 移除商品优惠价格和水票买赠规则
  deldiscount: `${host}/szmb/user/deldiscount`,
  // 查询设置优惠价格列表
  userwaterDiscountList: `${host}/szmb/user/userwater`,
  // 派单员修改订单
  updateCustomerorder: `${host}/szmb/updateordercontroller/updateorder`,
  // 杂牌桶抵扣查询桶品牌
  selectstorebrand: `${host}/szmb/productclasscontroller/selectstorebrand`,
  // 添加退桶单
  insertbucketbuck: `${host}/szmb/newdebtbuckcontroller/insertbucketbuck`,
  // 剩余欠桶信息
  selectPledgDect: `${host}/szmb/newdebtbuckcontroller/selectPledgDect`,

  // 查询派单员待接单数量
  selectdeliverycount: `${host}/szmb/deliveryinfo/selectdeliverycount`,

  // 确定不回桶
  finishDelivery: `${host}/szmb/deliveryinfo/finishDelivery`,

  // 分享H5
  shareH5toruzhu: `${host}/api/excal/selectdeliverytostore`,


  storeChackedState: `${host}/szmb/storeapplyfor/selectbyid`,

  newRemoveYouhui: `${host}/szmb/user/removePreferentialPrice`,


  newSendMsgCodeApi:`${host}/szmcuercontroller/sendsmsverificationcode`,
  /**
   * end yanxingwang
   */

  // 改变订单分组 POST
  orderGroupChange: `${host}/szmb/szmsendmembercontroller/changegroup`,
  orderBack: `${host}/szmb/szmsendmembercontroller/back`,
  selectusercollect: `${host}/szmb/newdebtbuckcontroller/selectusercollect`,
  updatePirurlByOrderId: `${host}/szmb/szmsendmembercontroller/updatePirurlByOrderId`,
  uploadPic: `${host}/uploadcontroller/upload`,
  uploadPicPingZheng: `${host}/uploadcontroller/uploadPingZheng`,
  refreshPhone: `${host}/jddj/refreshPhone`,
  selectchild: `${host}/szmb/szmsendmembercontroller/selectchild`,
  insertsendmember: `${host}/szmb/szmsendmembercontroller/insertsendmember`,
  orderreturnupdate: `${host}/szmb/orderreturn/update`,
  assignOrderToChild: `${host}/szmb/szmsendmembercontroller/assignorder`, // 指派订单给子送水员

  szmcordermaincontrollerupdateother: `${host}/szmcordermaincontroller/updateother`,
	szmcstoreinfo: `${host}/szmcstore/infoResult`,

  // 逆地理编码接口
  geocodereverse: `${host}/util/geocode/reverse`,

}

module.exports = config;
