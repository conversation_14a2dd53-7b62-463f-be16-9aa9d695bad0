# 订单来源连接商品件数配置功能

## 功能概述

为了解决套餐商品匹配到大众商品库时的件数问题，在订单来源连接配置中新增了**商品件数配置**功能。

## 问题背景

在订单来源连接配置中，外部平台（如抖店）的套餐商品可能包含多件商品，但匹配到内部商品库时只能关联到单个商品。例如：
- 抖店套餐：2瓶矿泉水套餐
- 内部商品库：单瓶矿泉水
- 问题：无法正确处理套餐中的商品数量

## 解决方案

新增`product_quantity`字段，用于配置该商品在套餐中的件数。

### 数据库变更

```sql
-- 添加商品件数字段
ALTER TABLE order_source_connect 
ADD COLUMN product_quantity INT DEFAULT 1 COMMENT '商品件数，用于套餐商品配置，默认为1件';

-- 添加约束
ALTER TABLE order_source_connect 
ADD CONSTRAINT chk_product_quantity CHECK (product_quantity > 0);
```

### 前端界面变更

1. **新增/编辑页面**：
   - 添加"商品件数"输入框
   - 支持1-999件的数量配置
   - 默认值为1件
   - 包含使用说明和注意事项

2. **列表页面**：
   - 新增"商品件数"列显示
   - 导出功能包含件数信息

### 后端接口变更

1. **实体类更新**：
   - `OrderSourceConnect`实体添加`productQuantity`字段
   - 添加对应的getter/setter方法

2. **数据库映射更新**：
   - 更新MyBatis映射文件
   - 包含insert、update、select操作

3. **Controller更新**：
   - 新增和更新时设置默认件数为1
   - 参数验证确保件数大于0

## 使用场景

### 场景1：普通商品
- 外部商品：单瓶矿泉水
- 内部商品：单瓶矿泉水
- 配置件数：1件

### 场景2：套餐商品
- 外部商品：2瓶矿泉水套餐
- 内部商品：单瓶矿泉水
- 配置件数：2件

### 场景3：组合套餐
- 外部商品：家庭装水套餐（包含5瓶水）
- 内部商品：单瓶水
- 配置件数：5件

## 配置步骤

1. **进入订单来源连接管理页面**
2. **新增或编辑关联关系**
3. **选择关联产品**
4. **设置商品件数**：
   - 根据套餐实际包含的商品数量设置
   - 确保件数与套餐描述一致
5. **保存配置**

## 注意事项

1. **库存影响**：
   - 商品件数直接影响库存扣减
   - 订单中1个套餐商品会扣减N件库存（N为配置的件数）

2. **价格计算**：
   - 件数配置可能影响价格计算逻辑
   - 需要确保价格计算考虑了件数因素

3. **数据一致性**：
   - 修改件数配置前请确认对现有订单的影响
   - 建议在业务低峰期进行配置修改

4. **默认值**：
   - 新建关联关系时默认件数为1
   - 现有数据会自动设置为1件

## 相关文件

### 前端文件
- `casaba-shuizhanmai-vue-master/business_platform_cs/src/views/setAdmin/orderSourceConnect.vue`
- `casaba-shuizhanmai-vue-master/business_platform_cs/src/views/setAdmin/orderSourceConnect-addorupdate.vue`

### 后端文件
- `shuizhanmai-old-master/waterstation-common/src/main/java/com/example/waterstationbuyproducer/entity/OrderSourceConnect.java`
- `shuizhanmai-old-master/waterstationbuy-producer/src/main/java/com/example/waterstationbuyproducer/szmb/newController/OrderSourceConnectController.java`
- `shuizhanmai-old-master/waterstationbuy-producer/src/main/resources/mapper/OrderSourceConnectMapper.xml`

### 数据库脚本
- `shuizhanmai-old-master/database_migration_add_product_quantity.sql`

## 测试建议

1. **功能测试**：
   - 测试新增关联关系时的件数配置
   - 测试编辑现有关联关系的件数修改
   - 测试件数验证（必须大于0）

2. **集成测试**：
   - 测试套餐商品下单时的库存扣减
   - 测试价格计算是否正确
   - 测试导出功能是否包含件数信息

3. **边界测试**：
   - 测试最大件数999的处理
   - 测试无效件数（0或负数）的处理
   - 测试空值的默认处理

## 已完成的功能实现

### 1. 数据库层面
- ✅ 添加`product_quantity`字段到`order_source_connect`表
- ✅ 设置字段约束和默认值
- ✅ 创建相关索引

### 2. 后端实现
- ✅ 更新`OrderSourceConnect`实体类，添加`productQuantity`字段
- ✅ 更新MyBatis映射文件，包含新字段的CRUD操作
- ✅ 更新Controller层，在新增和更新时处理件数配置
- ✅ 更新抖店订单处理逻辑，考虑商品件数进行价格计算
- ✅ 更新饿了么订单处理逻辑，考虑商品件数进行价格计算
- ✅ 自动创建关联关系时设置默认件数为1

### 3. 前端实现
- ✅ 新增/编辑页面添加商品件数输入框
- ✅ 列表页面显示商品件数信息
- ✅ 导出功能包含件数数据
- ✅ 表单验证确保件数在合理范围内

### 4. 业务逻辑集成
- ✅ 订单价格计算考虑商品件数
- ✅ 自动创建关联关系时的件数处理
- ✅ 日志记录包含件数信息

## 后续优化建议

1. **批量配置**：
   - 支持批量设置商品件数
   - 提供批量导入功能

2. **历史记录**：
   - 记录件数配置的修改历史
   - 提供配置变更审计功能

3. **智能推荐**：
   - 根据商品名称智能推荐件数
   - 提供常用件数的快捷选择

4. **报表统计**：
   - 统计不同件数配置的使用情况
   - 分析套餐商品的配置趋势

5. **库存管理**：
   - 考虑在库存扣减时应用商品件数
   - 提供库存预警功能
