-- 为订单来源连接表添加商品件数字段
-- 用于支持套餐商品的件数配置

-- 添加商品件数字段
ALTER TABLE order_source_connect 
ADD COLUMN product_quantity INT DEFAULT 1 COMMENT '商品件数，用于套餐商品配置，默认为1件';

-- 更新现有数据，设置默认值为1
UPDATE order_source_connect 
SET product_quantity = 1 
WHERE product_quantity IS NULL;

-- 添加字段约束
ALTER TABLE order_source_connect 
MODIFY COLUMN product_quantity INT NOT NULL DEFAULT 1 COMMENT '商品件数，用于套餐商品配置，默认为1件';

-- 添加检查约束，确保件数大于0
ALTER TABLE order_source_connect 
ADD CONSTRAINT chk_product_quantity CHECK (product_quantity > 0);

-- 创建索引以提高查询性能
CREATE INDEX idx_order_source_connect_quantity ON order_source_connect(product_quantity);

-- 查看表结构确认修改
DESCRIBE order_source_connect;
