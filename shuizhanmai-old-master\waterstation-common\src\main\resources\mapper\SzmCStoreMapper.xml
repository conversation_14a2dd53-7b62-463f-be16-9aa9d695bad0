<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.waterstationbuyproducer.dao.SzmCStoreMapper">
  <resultMap id="BaseResultMap" type="com.example.waterstationbuyproducer.entity.SzmCStore">
    <id column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_num" jdbcType="BIGINT" property="storeNum" />
    <result column="store_ame" jdbcType="VARCHAR" property="storeAme" />
    <result column="store_phone" jdbcType="VARCHAR" property="storePhone" />
    <result column="store_logo" jdbcType="VARCHAR" property="storeLogo" />
    <result column="store_servicetime" jdbcType="VARCHAR" property="storeServicetime" />
    <result column="store_servicestate" jdbcType="INTEGER" property="storeServicestate" />
    <result column="store_province" jdbcType="VARCHAR" property="storeProvince" />
    <result column="store_city" jdbcType="VARCHAR" property="storeCity" />
    <result column="store_area" jdbcType="VARCHAR" property="storeArea" />
    <result column="store_detailed" jdbcType="VARCHAR" property="storeDetailed" />
    <result column="store_starlevel" jdbcType="DOUBLE" property="storeStarlevel" />
    <result column="store_location" jdbcType="BIGINT" property="storeLocation" />
    <result column="store_shippingfee" jdbcType="DOUBLE" property="storeShippingfee" />
    <result column="store_createdate" jdbcType="TIMESTAMP" property="storeCreatedate" />
    <result column="store_apply_for_id" jdbcType="BIGINT" property="storeApplyForId" />
    <result column="r_1" jdbcType="VARCHAR" property="r1" />
    <result column="r_2" jdbcType="VARCHAR" property="r2" />
    <result column="r_3" jdbcType="VARCHAR" property="r3" />
    <result column="r_4" jdbcType="VARCHAR" property="r4" />
    <result column="r_5" jdbcType="VARCHAR" property="r5" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="scope" jdbcType="INTEGER" property="scope" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="paycode" jdbcType="VARCHAR" property="payCode" />
    <result column="wallet" jdbcType="DOUBLE" property="wallet" />
    <result column="identity" jdbcType="INTEGER" property="identity" />
    <result column="readcode" jdbcType="VARCHAR" property="readcode" />
    <result column="freeze" jdbcType="DOUBLE" property="freeze" />
    <result column="pledge" jdbcType="DOUBLE" property="pledge" />
    <result column="warning" jdbcType="INTEGER" property="warning" />
    <result column="subscribe" jdbcType="INTEGER" property="subscribe" />
    <result column="money" jdbcType="DOUBLE" property="money" />
    <result column="isend" jdbcType="INTEGER" property="isend" />
    <result column="tryout" jdbcType="TIMESTAMP" property="tryout" />
    <result column="distribution" jdbcType="INTEGER" property="distribution" />
    <result column="integral_state" jdbcType="INTEGER" property="integralState" />
    <result column="water_switch" jdbcType="INTEGER" property="waterSwitch" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="store_excal" jdbcType="VARCHAR" property="storeExcal" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_company" jdbcType="INTEGER" property="isCompany" />
    <result column="cid" jdbcType="VARCHAR" property="cid" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid"/>
    <result column="all_mask_state" jdbcType="INTEGER" property="allMaskState"/>
    <result column="deposit" jdbcType="INTEGER" property="deposit"/>
    <result column="companyTitle" jdbcType="VARCHAR" property="companyTitle"/>
    <result column="bank_card" jdbcType="VARCHAR" property="bankCard"/>
    <result column="opening_bank" jdbcType="VARCHAR" property="openingBank"/>
    <result column="tagline" jdbcType="VARCHAR" property="tagline"/>
    <result column="fixationphone" jdbcType="VARCHAR" property="fixationphone"/>
    <result column="role1" jdbcType="INTEGER" property="role1" />
    <result column="pid" jdbcType="BIGINT" property="pid" />
    <result column="invoice" jdbcType="DECIMAL" property="invoice" />
    <result column="putonginvoice" jdbcType="DECIMAL" property="putonginvoice" />
    <result column="zhuaninvoice" jdbcType="DECIMAL" property="zhuaninvoice" />
    <result column="wechat" jdbcType="VARCHAR" property="wechat" />
    <result column="minnumber" jdbcType="INTEGER" property="minnumber" />
    <result column="minprice" jdbcType="DECIMAL" property="minprice" />
    <result column="fenzhang" jdbcType="INTEGER" property="fenzhang" />
    <result column="xinren" jdbcType="INTEGER" property="xinren" />
    <result column="gongzhonghao" jdbcType="INTEGER" property="gongzhonghao" />
    <result column="fenzhangrate" jdbcType="DECIMAL" property="fenzhangrate" />
    <result column="fenzhangnumber" jdbcType="VARCHAR" property="fenzhangnumber" />
    <result column="mpopenid" jdbcType="VARCHAR" property="mpopenid" />
    <result column="lianying" jdbcType="INTEGER" property="lianying" />
    <result column="wuliu" jdbcType="VARCHAR" property="wuliu" />
    <result column="feilv" jdbcType="DECIMAL" property="feilv" />
    <result column="feiyong" jdbcType="DECIMAL" property="feiyong" />
    <result column="zitiwenan" jdbcType="VARCHAR" property="zitiwenan" />
    <result column="songhuowenan" jdbcType="VARCHAR" property="songhuowenan" />
    <result column="yatong" jdbcType="INTEGER" property="yatong" />
    <result column="kepu" jdbcType="INTEGER" property="kepu" />
    <result column="fanxian" jdbcType="INTEGER" property="fanxian" />
    <result column="tuanzhang" jdbcType="INTEGER" property="tuanzhang" />
    <result column="shuipiao" jdbcType="INTEGER" property="shuipiao" />
    <result column="lat" jdbcType="DECIMAL" property="lat" />
    <result column="lon" jdbcType="DECIMAL" property="lon" />
    <result column="nodeal" jdbcType="INTEGER" property="nodeal" />
    <result column="distance" jdbcType="DECIMAL" property="distance" />
    <result column="shoukuanma" jdbcType="VARCHAR" property="shoukuanma" />
    <result column="isphoto" jdbcType="INTEGER" property="isphoto" />



  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.example.waterstationbuyproducer.entity.SzmCStore">
    insert into szm_c_store (store_id, store_num, store_ame,
      store_phone, store_logo, store_servicetime,
      store_servicestate, store_province, store_city,
      store_area, store_detailed, store_starlevel,
      store_location, store_shippingfee, store_createdate,
      store_apply_for_id, r_1, r_2,
      r_3, r_4, r_5,content,img,code,password,scope,`state`,openid,paycode,wallet,`identity`,readcode,
      freeze,pledge,warning,subscribe,money,tryout,isend,distribution,integral_state,water_switch,shop_id,store_excal,company_id,
      is_company,cid,unionid,all_mask_state,deposit,bank_card,opening_bank,tagline,fixationphone,`role1`
      ,`pid`
      ,`invoice`
      ,`putonginvoice`
      ,`zhuaninvoice`
      ,`wechat`
      ,`minnumber`
      ,`minprice`
      ,`fenzhang`
      ,`fenzhangrate`
      ,`fenzhangnumber`
      ,`xinren`
      ,`mpopenid`
      ,`lianying`
      ,`wuliu`
      ,`feilv`
      ,`feiyong`
      ,`gongzhonghao`
      ,`zitiwenan`
      ,`songhuowenan`
      ,`yatong`
      ,`kepu`
      ,`fanxian`
      ,`tuanzhang`
      ,`shuipiao`
      ,`lat`
      ,`lon`
      ,`shoukuanma`
      ,`isphoto`
      )
    values (#{storeId,jdbcType=BIGINT}, #{storeNum,jdbcType=BIGINT}, #{storeAme,jdbcType=VARCHAR},
      #{storePhone,jdbcType=VARCHAR}, #{storeLogo,jdbcType=VARCHAR}, #{storeServicetime,jdbcType=VARCHAR},
      #{storeServicestate,jdbcType=INTEGER}, #{storeProvince,jdbcType=VARCHAR}, #{storeCity,jdbcType=VARCHAR},
      #{storeArea,jdbcType=VARCHAR}, #{storeDetailed,jdbcType=VARCHAR}, #{storeStarlevel,jdbcType=DOUBLE},
      #{storeLocation,jdbcType=BIGINT}, #{storeShippingfee,jdbcType=DOUBLE}, #{storeCreatedate,jdbcType=TIMESTAMP},
      #{storeApplyForId,jdbcType=BIGINT}, #{r1,jdbcType=VARCHAR}, #{r2,jdbcType=VARCHAR},
      #{r3,jdbcType=VARCHAR}, #{r4,jdbcType=VARCHAR}, #{r5,jdbcType=VARCHAR},
      #{content,jdbcType=VARCHAR}, #{img,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},#{password,jdbcType=VARCHAR},
      #{scope,jdbcType=INTEGER},#{state,jdbcType=INTEGER},#{openid,jdbcType=VARCHAR},#{payCode,jdbcType=VARCHAR},
     #{wallet,jdbcType=DOUBLE},#{identity,jdbcType=INTEGER},#{readcode,jdbcType=VARCHAR},#{freeze,jdbcType=DOUBLE},
     #{pledge,jdbcType=DOUBLE},#{warning,jdbcType=INTEGER},
     #{subscribe,jdbcType=INTEGER},#{money,jdbcType=INTEGER},#{tryout,jdbcType=TIMESTAMP},#{isend,jdbcType=INTEGER},#{distribution,jdbcType=INTEGER},
     #{integralState,jdbcType=INTEGER},#{waterSwitch,jdbcType=INTEGER},#{shopId,jdbcType=BIGINT},#{storeExcal,jdbcType=VARCHAR},#{companyId,jdbcType=BIGINT},
     #{isCompany,jdbcType=INTEGER},#{cid,jdbcType=VARCHAR},#{unionid,jdbcType=VARCHAR},#{allMaskState,jdbcType=INTEGER},#{deposit,jdbcType=DOUBLE},
     #{bankCard,jdbcType=VARCHAR},#{openingBank,jdbcType=VARCHAR},#{tagline,jdbcType=VARCHAR},#{fixationphone,jdbcType=VARCHAR},#{role1}
     ,#{pid}
     ,#{invoice}
     ,#{putonginvoice}
     ,#{zhuaninvoice}
     ,#{wechat}
     ,#{minnumber}
     ,#{minprice}
     ,#{fenzhang}
     ,#{fenzhangrate}
     ,#{fenzhangnumber}
     ,#{xinren}
     ,#{mpopenid}
     ,#{lianying}
     ,#{wuliu}
     ,#{feilv}
     ,#{feiyong}
     ,#{gongzhonghao}
     ,#{zitiwenan}
     ,#{songhuowenan}
     ,#{yatong}
     ,#{kepu}
     ,#{fanxian}
     ,#{tuanzhang}
     ,#{shuipiao}
     ,#{lat}
     ,#{lon}
     ,#{shoukuanma}
     ,#{isphoto}
     )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.example.waterstationbuyproducer.entity.SzmCStore">
    update szm_c_store
    <trim prefix="set" suffixOverrides=",">
      <if test="storeAme!=null">store_ame = #{storeAme,jdbcType=VARCHAR},</if>
      <if test="storeLogo!=null"> store_logo = #{storeLogo,jdbcType=VARCHAR},</if>
      <if test="storeServicetime!=null">store_servicetime = #{storeServicetime,jdbcType=VARCHAR},</if>
      <if test="storePhone!=null">store_phone = #{storePhone,jdbcType=VARCHAR},</if>
      <if test="storeServicestate!=null"> store_servicestate = #{storeServicestate,jdbcType=INTEGER},</if>
      <if test="storeProvince!=null"> store_province = #{storeProvince,jdbcType=VARCHAR},</if>
      <if test="storeCity!=null"> store_city = #{storeCity,jdbcType=VARCHAR},</if>
      <if test="storeArea!=null"> store_area = #{storeArea,jdbcType=VARCHAR},</if>
      <if test="storeDetailed!=null">  store_detailed = #{storeDetailed,jdbcType=VARCHAR},</if>
      <if test="storeStarlevel!=null"> store_starlevel = #{storeStarlevel,jdbcType=DOUBLE},</if>
      <if test="storeLocation!=null"> store_location = #{storeLocation,jdbcType=BIGINT},</if>
      <if test="storeShippingfee!=null">store_shippingfee = #{storeShippingfee,jdbcType=DOUBLE},</if>
      <if test="storeCreatedate!=null"> store_createdate = #{storeCreatedate,jdbcType=TIMESTAMP},</if>
      <if test="storeApplyForId!=null">store_apply_for_id = #{storeApplyForId,jdbcType=BIGINT},</if>
      <if test="r1!=null"> r_1 = #{r1,jdbcType=VARCHAR},</if>
      <if test="r2!=null"> r_2 = #{r2,jdbcType=VARCHAR},</if>
      <if test="r3!=null">r_3 = #{r3,jdbcType=VARCHAR},</if>
      <if test="r4!=null"> r_4 = #{r4,jdbcType=VARCHAR},</if>
      <if test="r5!=null"> r_5 = #{r5,jdbcType=VARCHAR},</if>
      <if test="content!=null"> content = #{content,jdbcType=VARCHAR},</if>
      <if test="img!=null"> img = #{img,jdbcType=VARCHAR},</if>
      <if test="code!=null"> code = #{code,jdbcType=VARCHAR},</if>
      <if test="password!=null"> password = #{password,jdbcType=VARCHAR},</if>
      <if test="scope!=null">`scope` = #{scope,jdbcType=INTEGER},</if>
      <if test="state!=null"> `state` = #{state,jdbcType=INTEGER},</if>
      <if test="openid!=null"> openid = #{openid,jdbcType=VARCHAR},</if>
      <if test="payCode!=null">paycode = #{payCode,jdbcType=INTEGER},</if>
      <if test="wallet!=null"> wallet = #{wallet,jdbcType=DOUBLE},</if>
      <if test="identity!=null"> `identity` = #{identity,jdbcType=INTEGER},</if>
      <if test="readcode!=null"> readcode = #{readcode,jdbcType=VARCHAR},</if>
      <if test="freeze!=null"> freeze = #{freeze,jdbcType=DOUBLE},</if>
      <if test="pledge!=null"> pledge = #{pledge,jdbcType=DOUBLE},</if>
      <if test="warning!=null"> warning = #{warning,jdbcType=INTEGER},</if>
      <if test="subscribe!=null"> subscribe = #{subscribe,jdbcType=INTEGER},</if>
      <if test="money!=null"> money = #{money,jdbcType=DOUBLE},</if>
      <if test="tryout!=null"> tryout = #{tryout,jdbcType=TIMESTAMP},</if>
      <if test="isend!=null"> isend = #{isend,jdbcType=INTEGER},</if>
      <if test="distribution!=null"> distribution = #{distribution,jdbcType=INTEGER},</if>
      <if test="integralState!=null"> integral_state = #{integralState,jdbcType=INTEGER},</if>
      <if test="waterSwitch!=null"> water_switch = #{waterSwitch,jdbcType=INTEGER},</if>
      <if test="shopId!=null"> shop_id = #{shopId,jdbcType=BIGINT},</if>
      <if test="storeExcal!=null"> store_excal = #{storeExcal,jdbcType=BIGINT},</if>
      <if test="companyId!=null"> company_id= #{companyId,jdbcType=BIGINT},</if>
      <if test="isCompany!=null"> is_company= #{isCompany,jdbcType=INTEGER},</if>
      <if test="cid!=null"> cid= #{cid,jdbcType=VARCHAR},</if>
       <if test="unionid!=null">unionid = #{unionid,jdbcType=VARCHAR},</if>
        <if test="allMaskState!=null">all_mask_state = #{allMaskState,jdbcType=INTEGER},</if>
      <if test="deposit!=null">deposit = #{deposit,jdbcType=DOUBLE},</if>
      <if test="companyTitle!=null">companyTitle = #{companyTitle,jdbcType=VARCHAR},</if>
      <if test="bankCard!=null">bank_card = #{bankCard,jdbcType=VARCHAR},</if>
      <if test="openingBank!=null">opening_bank = #{openingBank,jdbcType=VARCHAR},</if>
      <if test="tagline!=null">tagline = #{tagline,jdbcType=VARCHAR},</if>
      <if test="fixationphone!=null">fixationphone = #{fixationphone,jdbcType=VARCHAR},</if>
      <if test="role1!=null">`role1` = #{role1},</if>
      <if test="pid!=null">`pid` = #{pid},</if>
      <if test="invoice!=null">`invoice` = #{invoice},</if>
      <if test="putonginvoice!=null">`putonginvoice` = #{putonginvoice},</if>
      <if test="zhuaninvoice!=null">`zhuaninvoice` = #{zhuaninvoice},</if>
      <if test="wechat!=null">`wechat` = #{wechat},</if>
      <if test="minnumber!=null">`minnumber` = #{minnumber},</if>
      <if test="minprice!=null">`minprice` = #{minprice},</if>
      <if test="fenzhang!=null">`fenzhang` = #{fenzhang},</if>
      <if test="fenzhangrate!=null">`fenzhangrate` = #{fenzhangrate},</if>
      <if test="fenzhangnumber!=null">`fenzhangnumber` = #{fenzhangnumber},</if>
      <if test="xinren!=null">`xinren` = #{xinren},</if>
      <if test="mpopenid!=null">`mpopenid` = #{mpopenid},</if>
      <if test="lianying!=null">`lianying` = #{lianying},</if>
      <if test="feilv!=null">`feilv` = #{feilv},</if>
      <if test="feiyong!=null">`feiyong` = #{feiyong},</if>
      <if test="wuliu!=null">`wuliu` = #{wuliu},</if>
      <if test="gongzhonghao!=null">`gongzhonghao` = #{gongzhonghao},</if>
      <if test="zitiwenan!=null">`zitiwenan` = #{zitiwenan},</if>
      <if test="songhuowenan!=null">`songhuowenan` = #{songhuowenan},</if>
      <if test="yatong!=null">`yatong` = #{yatong},</if>
      <if test="kepu!=null">`kepu` = #{kepu},</if>
      <if test="fanxian!=null">`fanxian` = #{fanxian},</if>
      <if test="tuanzhang!=null">`tuanzhang` = #{tuanzhang},</if>
      <if test="shuipiao!=null">`shuipiao` = #{shuipiao},</if>
      <if test="lat!=null">`lat` = #{lat},</if>
      <if test="lon!=null">`lon` = #{lon},</if>
      <if test="shoukuanma!=null">`shoukuanma` = #{shoukuanma},</if>
      <if test="isphoto!=null">`isphoto` = #{isphoto},</if>
      </trim>
    where store_id = #{storeId,jdbcType=BIGINT}
  </update>
  <update id="updatebyStorePhone">
    update szm_c_store
    set password = #{password}
    where store_phone = #{phone}
  </update>
  <update id="updatePid">
    update szm_c_store
    set pid = #{pid}
    where store_id = #{storeId}
  </update>
  <sql id="result">store_id, store_num, store_ame, store_phone, store_logo, store_servicetime,
    store_servicestate, store_province, store_city, store_area, store_detailed, store_starlevel,
    store_location, store_shippingfee, store_createdate, store_apply_for_id, r_1, r_2,
    r_3, r_4, r_5, content, img, `code`,`password`,scope,`state`,openid,paycode,wallet,`identity`,readcode,
    freeze,pledge,warning,subscribe,money,tryout,isend,distribution,integral_state,water_switch,shop_id,store_excal,company_id,
    is_company,cid,unionid,all_mask_state,deposit,companyTitle,bank_card,opening_bank,tagline,fixationphone,`role1`
        ,`pid`
        ,`invoice`
        ,`putonginvoice`
        ,`zhuaninvoice`
        ,`wechat`
        ,`minnumber`
        ,`minprice`
        ,`fenzhang`
        ,`fenzhangrate`
        ,`fenzhangnumber`
        ,`xinren`
        ,`mpopenid`
        ,`lianying`
        ,`wuliu`
        ,`feilv`
        ,`feiyong`
        ,`gongzhonghao`
        ,`zitiwenan`
        ,`songhuowenan`
        ,`yatong`
        ,`kepu`
        ,`fanxian`
        ,`tuanzhang`
        ,`shuipiao`
        ,`lat`
        ,`lon`
        ,`shoukuanma`
        ,`isphoto`
        </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select <include refid="result" />
    from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </select>

  <select id="findMonthPayList" resultType="java.util.Map" parameterType="com.example.waterstationbuyproducer.vo.Turnover.PaymentSerchVO">
        <!--SELECT
            a.order_number as orderNumber,
            DATE_FORMAT(a.create_time, '%Y/%m/%d %T') as createTime,
            round(a.total_money, 2) as totalAmount,
            a.r_1
        FROM order_yf AS a
        WHERE a.r_1 = '0'
        AND a.redis_money = 0
        AND a.store_id = #{storeId}
        AND a.user_id = #{userId}
        order by create_time desc-->
        select b.fund_refund_list_num as num
            , DATE_FORMAT(b.fund_refund_list_date, '%Y/%m/%d %T') as createTime
            , round(b.fund_refund_list_price, 2) as totalAmount
            , b.r_1
        from smz_c_fund_watercourse a
        inner join smz_c_fund_refund_list b on a.fund_watercourse_id = b.fund_watercourse_id
        where a.user_id = #{userId} and a.store_id = #{storeId}
        order by b.fund_refund_list_date desc
  </select>

  <select id="selectCreatedateByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select store_id, store_createdate
    from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </select>


  <select id="selectAll" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where `state` = 2 and (`identity`= 1 or `identity`= 2)
  </select>

  <select id="selectByNameLike" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where `state` = 2 and (`identity`= 1 or `identity`= 2)
    <if test="name!=null and name != '' and name !='undefined'">
      and store_ame like concat('%',#{name},'%')
    </if>

  </select>

  <select id="selectStore" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where store_num = #{storeNum}
  </select>

  <select id="selectStoreId" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where store_id  = #{storeId}
  </select>
  <select id="selectCountByPhone" resultType="int" >
      select count(1)
      from szm_c_store where store_phone  = #{phone}
    </select>
    <select id="loginNoPassword"  resultMap="BaseResultMap">
     select  <include refid="result" />
    from szm_c_store where store_phone  = #{phone}
    </select>
    <select id="login"  resultMap="BaseResultMap">
     select  <include refid="result" />
    from szm_c_store where store_phone  = #{phone} and password= #{password}
    </select>
  <select id="selectReadcode" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where readcode = #{readcode}
  </select>

  <select id="selectByOpenId" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where openid = #{openId}
  </select>

  <select id="selectByUnionId" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where unionid = #{unionId}
  </select>

  <select id="selectByUnionIds" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where unionid in
    <foreach item="id" collection="unionid" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

    <select id="selectCode" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where r_1 = #{code}
  </select>
    <select id="selectByStoreIdAndPhone" resultMap="BaseResultMap">
      select  <include refid="result" />
    from szm_c_store where store_id = #{storeId} and store_phone = #{phone}
    </select>
  <select id="selectSendStoreAll" resultType="com.example.waterstationbuyproducer.entity.SzmCStore">
    select store_phone
    from szm_c_store
  </select>
  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where company_id = #{companyId}
  </select>
  <select id="selectStorephonecount" resultType="java.lang.Integer">
    select count(1)
      from szm_c_store where store_phone  = #{phone} and store_id != #{storeId}
  </select>
    <select id="selectByPhone" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where store_phone  = #{phone}
  </select>

  <select id="selectPhoneById" resultType="java.lang.String">
    select store_phone
    from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </select>

  <select id="selectLocationById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </select>

  <select id="selectOtherById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select store_id,store_phone,cid,isend
    from szm_c_store
    where store_id = #{storeId,jdbcType=BIGINT}
  </select>
  <select id="getProductClassifyNameByStoreId" resultType="java.lang.Integer">
    SELECT COUNT(1)
    from szm_c_store ss LEFT JOIN szm_c_product_classify scc
    ON ss.store_id = scc.store_id
    WHERE ss.store_id = #{storeId} AND scc.product_classify_name = #{className} ;
  </select>
  <select id="getProductClassifyNameAndStateByStoreId" resultType="java.lang.Integer">
    SELECT COUNT(1)
    from szm_c_store ss LEFT JOIN szm_c_product_classify scc
    ON ss.store_id = scc.store_id
    WHERE ss.store_id = #{storeId} AND scc.product_classify_name = #{className} AND scc.state = 0;
  </select>

  <select id="selectStoreAme" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
      store_ame
    from
      szm_c_store
    where
      store_id = #{storeId,jdbcType=BIGINT}
  </select>


  <select id="selectStoreAndState" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where `identity`= #{state}
  </select>


  <select id="selectByState" resultMap="BaseResultMap">
    select 
    
        <if test="lat != null and lon != null">
            (6371 * ACOS(
            COS(RADIANS(#{lat})) * COS(RADIANS(lat)) * COS(RADIANS(lon) - RADIANS(#{lon})) +
            SIN(RADIANS(#{lat})) * SIN(RADIANS(lat))
            )) AS distance,
        </if>
     <include refid="result" />
    from szm_c_store where `state`= #{state}
    <if test="lat != null and lon != null">
        order by distance is not null desc ,distance, store_createdate 
    </if>
  </select>

  <select id="selectNearbyShops" resultType="java.lang.Integer">
      SELECT
	      count(1)
      FROM
          szm_c_user
      WHERE
          user_id = #{userId}
          and  r_2 = 0
          and (r_4 = 2 or r_4 is null)
          ORDER BY create_time desc
  </select>
    <select id="list" resultMap="BaseResultMap" parameterType="java.util.Map" >
      select  aa.store_id, aa.store_num, aa.store_ame, aa.store_phone, aa.store_logo, aa.store_servicetime,
    aa.store_servicestate, aa.store_province, aa.store_city, aa.store_area, aa.store_detailed, aa.store_starlevel,
    aa.store_location, aa.store_shippingfee, aa.store_createdate, aa.store_apply_for_id, aa.r_1, aa.r_2,
    aa.r_3, aa.r_4, aa.r_5, aa.content, aa.img, aa.`code`,aa.`password`,aa.scope,aa.`state`,aa.openid,aa.paycode,aa.wallet,aa.`identity`,aa.readcode,
    aa.freeze,aa.pledge,aa.warning,aa.subscribe,aa.money,aa.tryout,aa.isend,aa.distribution,aa.integral_state,aa.water_switch,aa.shop_id,aa.store_excal,aa.company_id,
    aa.is_company,aa.cid,aa.unionid,aa.all_mask_state,aa.deposit,aa.companyTitle,aa.bank_card,aa.opening_bank,aa.tagline,aa.fixationphone,aa.`role1`
        ,aa.`pid`
        ,aa.`invoice`
        ,aa.`putonginvoice`
        ,aa.`zhuaninvoice`
        ,aa.`wechat`
        ,aa.`minnumber`
        ,aa.`minprice`
        ,aa.`fenzhang`
        ,aa.`fenzhangrate`
        ,aa.`fenzhangnumber`
        ,aa.`xinren`
        ,aa.`mpopenid`
        ,aa.`lianying`
        ,aa.`wuliu`
        ,aa.`feilv`
        ,aa.`feiyong`
        ,aa.`gongzhonghao`
        ,aa.`zitiwenan`
        ,aa.`songhuowenan`
        ,aa.`yatong`
        ,aa.`kepu`
        ,aa.`fanxian`
        ,aa.`tuanzhang`
        ,aa.`shuipiao`
        ,aa.`lat`
        ,aa.`lon`
        ,aa.`shoukuanma`
        ,aa.`isphoto`
        ,count(1) as `nodeal`
      from szm_c_store aa LEFT JOIN szm_c_order_main bb ON aa.store_id = bb.store_id AND bb.order_status  = 2  where
        1 =1 
      <if test="name != null and name != ''"> and aa.store_ame like CONCAT("%",#{name},"%") </if>
      <if test="mobile != null and mobile != ''"> and aa.store_phone like CONCAT("%",#{mobile},"%") </if>
      <if test="role1 != null and role1 != ''"> and aa.role1 = #{role1} </if>
      <if test="identity != null and identity != ''"> and aa.identity = #{identity} </if>
      <if test="state != null and state != ''"> and aa.state = #{state} </if>
      <if test="pid != null and pid != ''"> and (aa.pid = #{pid} or aa.store_id = #{storeId})</if>
      <if test="storeCity != null and storeCity != ''"> and aa.store_city = #{storeCity} </if>
      <if test="storeArea != null and storeArea != ''"> and aa.store_area = #{storeArea} </if>
      <if test="storeProvince != null and storeProvince != ''"> and aa.store_province = #{storeProvince} </if>
      group by aa.store_id
      <if test="sort != null and sort == 2">
        order by count(1) desc
      </if>
      <if test="sort != null and sort == 3">
        order by aa.store_createdate desc
      </if>
    </select>

    <select id="findByRole1" resultMap="BaseResultMap">
      select  <include refid="result" />
      from szm_c_store where
        role1 = #{role1}
    </select>
  <select id="findChild" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    pid = #{storeId}
  </select>
  <select id="findByIds" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    store_id in
    <foreach item="id" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="findByStoreCity" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    store_city = #{storeCity}
  </select>
  <select id="findByStoreArea" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    store_area = #{storeArea}
  </select>
  <select id="selectCanStore" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    (role1 = 4 or (role1 = 3 and store_city = #{code2}) or (role1 = 2 and store_area = #{code}))
    and state = 2
  </select>
  <select id="selectByLianying"  resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    lianying = #{lianying}
  </select>
  <select id="selectByStoreCity" resultMap="BaseResultMap">
    select  <include refid="result" />
    from szm_c_store where
    store_city = #{city} and state = 2
  </select>

  <update id="updateShoukuanma">
    update szm_c_store set shoukuanma = #{shoukuanma} where store_id = #{storeId}
  </update>
</mapper>