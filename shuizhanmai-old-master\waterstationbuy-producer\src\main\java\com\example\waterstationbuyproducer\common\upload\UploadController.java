/**
 * @姓名 于瑞杰
 * @版本号 1.1.4
 * @日期 2019/6/25
 */
package com.example.waterstationbuyproducer.common.upload;

import com.example.waterstationbuyproducer.util.OrderNumUtil;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.util.wx.weixin.config.WxPayConfig;
import io.swagger.annotations.Api;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @description 上传图片
 * @date 2019/3/14
 */
@RestController
@RequestMapping("uploadcontroller")
@Api(value = "api/uploadcontroller/", description = "上传图片")
public class UploadController {

    /**
     * 上传图片
     */
    @PostMapping("/upload")
    public ResultBean upload(@RequestParam("file") MultipartFile file) {
        ResultBean resultBean = new ResultBean();
        if (file.isEmpty()) {
            return resultBean.error("上传失败，请选择文件");
        }
        String name = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = OrderNumUtil.getOrderIdByTime(null)+"."+name;
        Integer state = 0;
        if(fileName.contains(".png")){
            state = 1;
        }
        String filePath = "/opt/static/upload/";
        file.getName();
        File dest = new File(filePath + fileName);
        try {
            file.transferTo(dest);
            String[] format = {"AVI","mov","rmvb","rm","FLV","mp4","3GP"};
            boolean flag = false;
            for (int i = 0; i <format.length ; i++) {
                if (format[i].equalsIgnoreCase(name)) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                if (state == 0){
                    Thumbnails.of(dest).scale(1f).outputQuality(0.25f).toFile(dest);
                }
            }
            return resultBean.success("/upload/" + fileName);
        } catch (IOException e) {
            return new ResultBean(e);
        }
    }
    @PostMapping("/uploadPingZheng")
    public ResultBean uploadPingZheng(@RequestParam("file") MultipartFile file) {
        ResultBean resultBean = new ResultBean();
        if (file.isEmpty()) {
            return resultBean.error("上传失败，请选择文件");
        }
        String name = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = OrderNumUtil.getOrderIdByTime(null)+"."+name;
        Integer state = 0;
        if(fileName.contains(".png")){
            state = 1;
        }
        String filePath = "/opt/static/upload/pingzheng/";
        file.getName();
        File dest = new File(filePath + fileName);
        try {
            file.transferTo(dest);
            String[] format = {"AVI","mov","rmvb","rm","FLV","mp4","3GP"};
            boolean flag = false;
            for (int i = 0; i <format.length ; i++) {
                if (format[i].equalsIgnoreCase(name)) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                if (state == 0){
                    Thumbnails.of(dest).scale(1f).outputQuality(0.25f).toFile(dest);
                }
            }
            return resultBean.success("/upload/pingzheng/" + fileName);
        } catch (IOException e) {
            return new ResultBean(e);
        }
    }
    /**
     * 上传图片
     */
    @PostMapping("/productlibrary")
    public ResultBean productLibrary(@RequestParam("file") MultipartFile file) {
        ResultBean resultBean = new ResultBean();
        if (file.isEmpty()) {
            return resultBean.error("上传失败，请选择文件");
        }
        String name = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = OrderNumUtil.getOrderIdByTime(null)+"."+name;
        String filePath = "/opt/static/product_library/";
        file.getName();
        File dest = new File(filePath + fileName);
        try {
            file.transferTo(dest);
            return resultBean.success("/product_library/" + fileName);
        } catch (IOException e) {
            return new ResultBean(e);
        }
    }
    /**
     * 上传图片
     */
    @PostMapping("/appupload")
    public ResultBean appUpload(@RequestParam("file") MultipartFile file) {
        ResultBean resultBean = new ResultBean();
        if (file.isEmpty()) {
            return resultBean.error("上传失败，请选择文件");
        }
        String name = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = OrderNumUtil.getOrderIdByTime(null)+"."+name;
        String filePath = "/opt/static/product_library/";
        file.getName();
        File dest = new File(filePath + fileName);
        try {
            file.transferTo(dest);
//            https://waterstation.com.cn/szm/AppVersion/b110.wgt
            if (WxPayConfig.isTest.equals("test")) {
                return resultBean.success("https://waterstation.com.cn/szm/AppVersion/" + fileName);
            }
            return resultBean.success("https://waterstation.com.cn/szm/AppVersion/" + fileName);
        } catch (IOException e) {
            return new ResultBean(e);
        }
    }
}
