package com.example.waterstationbuyproducer.szmb.export;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.example.waterstationbuyproducer.szmb.enume.OrderSourceEnum;
import com.example.waterstationbuyproducer.util.DateUtils;
import com.example.waterstationbuyproducer.vo.RetuenOrderList;
import com.google.common.collect.ImmutableList;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class OrderMainExportService {
    /**
     * 不创建对象的写
     */
    public void write(List<RetuenOrderList> contractEntities, HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("订单数据导出_" + DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_NO_LINE), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition",
                    "attachment;filename*=utf-8''" + fileName + ".xlsx");
            Long storeId = 0L;
            if(!CollectionUtils.isEmpty(contractEntities)) {
                storeId = contractEntities.get(0).getStoreId();
            }
            EasyExcel
                    .write(response.getOutputStream())
                    .head(head(storeId))
                    .autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("订单数据")
                    .doWrite(dataList(contractEntities));
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<String, String>();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    public static List<List<String>> head( Long storeId) {
        List<List<String>> list = new ArrayList<>();
        list.add(ImmutableList.of("订单来源"));
        list.add(ImmutableList.of("所属商家"));
        list.add(ImmutableList.of("商家电话"));
        list.add(ImmutableList.of("订单编号"));
        list.add(ImmutableList.of("联系人"));
        list.add(ImmutableList.of("联系方式"));
        list.add(ImmutableList.of("订单地址"));
        list.add(ImmutableList.of("下单日期"));
        list.add(ImmutableList.of("送达日期"));
        list.add(ImmutableList.of("订单总金额"));
        list.add(ImmutableList.of("订单总数量"));
        list.add(ImmutableList.of("订单信息"));
        list.add(ImmutableList.of("订单状态"));
        list.add(ImmutableList.of("送水员信息"));
        list.add(ImmutableList.of("订单来源"));
        if(storeId.equals(1841L)) {
        list.add(ImmutableList.of("顺丰单号"));

        }
        return list;
    }


    public static List<List<Object>> dataList(List<RetuenOrderList> contractEntities) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        contractEntities.forEach(e -> {
            List<Object> data = new ArrayList<Object>();
            String name = "";
            if (!CollectionUtils.isEmpty(e.getList())) {
                name = e.getList().get(0).getOrderShopDeatilList().stream().map(f -> {
                    return f.getTitle() + "x" + f.getShopNumber();
                }).collect(Collectors.joining(";"));
            }
            if (!CollectionUtils.isEmpty(e.getGroupShopList())) {
                name = e.getGroupShopList().stream().map(f -> {
                    return f.getGroupName() + "x" + f.getGroupNumber();
                }).collect(Collectors.joining(";"));
            }
            data.add(e.getOrderSourceName());
            data.add(e.getStoreName());
            data.add(e.getStoreMobile());
            data.add(e.getOrderNumber());
            data.add(e.getUserName());
            data.add(e.getUserPhone());
            data.add(e.getUserAddress());
            data.add(e.getOrderDate() == null ? "" : DateUtils.format(e.getOrderDate(), DateUtils.DATE_PATTERN_CHINA));
            data.add(e.getFinishTime() == null ? "" : DateUtils.format(e.getFinishTime(), DateUtils.DATE_PATTERN_CHINA));
            data.add(e.getOrderPrice());
            data.add(e.getOrderTotalNumber());
            data.add(name);
            data.add(e.getNewOrderState());
            data.add(StringUtils.isEmpty(e.getDeliveryName()) ? "" :( e.getDeliveryName() +  "/" + e.getDeliveryMobile()));
            data.add(e.getOrdersource() == null ? "自营" : OrderSourceEnum.getValueByCode(e.getOrdersource()));
            data.add(e.getShipsn());
            list.add(data);
        });
        return list;
    }

}
