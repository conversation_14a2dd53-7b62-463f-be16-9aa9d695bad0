package com.example.waterstationbuyproducer.szmb.newController;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.doudian.open.api.instantShopping_createDelivery.InstantShoppingCreateDeliveryRequest;
import com.doudian.open.api.instantShopping_createDelivery.InstantShoppingCreateDeliveryResponse;
import com.doudian.open.api.instantShopping_createDelivery.param.InstantShoppingCreateDeliveryParam;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusRequest;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusResponse;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.param.InstantShoppingNotifyDeliveryStatusParam;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusRequest;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusResponse;
import com.doudian.open.api.instantShopping_notifyPickingStatus.param.InstantShoppingNotifyPickingStatusParam;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddRequest;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddResponse;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_searchList.OrderSearchListRequest;
import com.doudian.open.api.order_searchList.OrderSearchListResponse;
import com.doudian.open.api.order_searchList.data.OrderSearchListData;
import com.doudian.open.api.order_searchList.data.PostAddr;
import com.doudian.open.api.order_searchList.data.ShopOrderListItem;
import com.doudian.open.api.order_searchList.data.SkuOrderListItem;
import com.doudian.open.api.order_searchList.param.OrderSearchListParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.GlobalConfig;
import com.doudian.open.gson.*;
import com.doudian.open.gson.internal.LinkedTreeMap;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.entity.*;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmb.service.order.OrderSourceService;
import com.example.waterstationbuyproducer.szmc.service.SzmCUserService;
import com.example.waterstationbuyproducer.util.DateUtils;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.util.StoreIdUtil;
import com.example.waterstationbuyproducer.util.StringUtils;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.redis.RedisUtil;
import com.example.waterstationbuyproducer.util.sms.RemindSMS;
import com.example.waterstationbuyproducer.util.sms.UtilSMS;
import o2o.openplatform.sdk.util.HttpUtil;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @describe:
 * @program: shuizhanmai-old-master
 * @author: cjy
 * @create: 2024-10-08 14:58
 */
@RestController
@RequestMapping("doudian")
public class DoudianController {

    private static final Logger logger = LoggerFactory.getLogger(DoudianController.class);
    @Autowired
    private RedisUtil redisUtil;
    String token = "b3ab79e7-b263-40f9-bc30-d6dda3f88c85";
    String appKey = "7418481390435436070";
    String appSecret = "be244dec-1caa-4f1e-91f0-ff9e0034afc7";

    Long shopId = 190165892L;

    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCUserService szmCUserService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;
    
    @Autowired
    private OrderSourceService orderSourceService;


    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;

    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;

    @Autowired
    private SmsMasterMapper smsMasterMapper;

    @Autowired
    private SmsRecordMapper smsRecordMapper;

    @Autowired
    private StoreMsgMapper storeMsgMapper;

    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;

    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;
    @Autowired
    private StoreIdUtil storeIdUtil;

    @Autowired
    private OrderSourceConnectMapper orderSourceConnectMapper;

    @Autowired
    private SzmCProductNewMapper szmCProductNewMapper;

    @RequestMapping("delivery")
    public ResultBean delivery(String orderId,Long status,String username,String mobile,String lat,String lon) throws Exception {
        GlobalConfig.initAppKey(appKey);
        GlobalConfig.initAppSecret(appSecret);
        //获取access_token方法
        AccessToken accessToken = AccessTokenBuilder.build(shopId); //入参为shopId
        InstantShoppingNotifyDeliveryStatusRequest request = new InstantShoppingNotifyDeliveryStatusRequest();
        InstantShoppingNotifyDeliveryStatusParam param = request.getParam();
        param.setShopOrderId(orderId);
        param.setDistributionCode("MERCHANT");
        param.setDistributionDeliveryId(orderId);
        param.setRiderPhoneType(0);
        if (!status.equals(101L)) {
           param.setRiderName(username);
           param.setRiderPhone(mobile);
           param.setRiderLatitude(lat);
           param.setRiderLongitude(lon);
        }
        param.setReportTime(new Date().getTime() / 1000);
        param.setUpdateTime(new Date().getTime() / 1000);
        param.setStatus(status);
        request.setParam(param);
        InstantShoppingNotifyDeliveryStatusResponse response = request.execute(accessToken);


        return new ResultBean().success(response.getData());
    }
    @RequestMapping("fahuo")
    public ResultBean fahuo(String orderId) throws Exception {
        // Long status = 102L;
        GlobalConfig.initAppKey(appKey);
        GlobalConfig.initAppSecret(appSecret);
        //获取access_token方法
        AccessToken accessToken = AccessTokenBuilder.build(shopId); //入参为shopId
        OrderLogisticsAddRequest request = new OrderLogisticsAddRequest();
        OrderLogisticsAddParam param = request.getParam();
        param.setOrderId(orderId);
        param.setCompanyCode("MERCHANT");
        param.setCompany("商家自配送");
        param.setStoreId(190337589L);
        param.setLogisticsCode(orderId);
        OrderLogisticsAddResponse response = request.execute(accessToken);

        return new ResultBean().success(response.getData());
    }

    @RequestMapping("createDelivery")
    public ResultBean createDelivery(String orderId,Long status,String username,String mobile,String lat,String lon) throws Exception {
        GlobalConfig.initAppKey(appKey);
        GlobalConfig.initAppSecret(appSecret);
        //获取access_token方法
        AccessToken accessToken = AccessTokenBuilder.build(shopId); //入参为shopId


        // 收集参数
        String host = "https://openapi-fxg.jinritemai.com";
        String method = "instantShopping.distribution/notifyDeliveryStatus";

        long timestamp = System.currentTimeMillis() / 1000;

        Map<String, Object> m = new HashMap<>();
        m.put("delivery_id",orderId);
        m.put("distributor_delivery_id",orderId);
        m.put("distributor_code","MERCHANT");
        m.put("status",status);
        m.put("update_time",new Date().getTime() / 1000);
        m.put("notify_time",new Date().getTime() / 1000);

        if (!status.equals(101L)) {
            m.put("rider_name",username);
            m.put("rider_phone",mobile);
            m.put("rider_lat",Double.valueOf(lat));
            m.put("rider_lng",Double.valueOf(lon));
        }
        // 序列化参数
        String paramJson = marshal(m);
        // 计算签名
        String signVal = sign(appKey, appSecret, method, timestamp, paramJson);
        // 发起请求
        JSONObject responseVal = fetch(appKey, host, method, timestamp, paramJson, accessToken.getAccessToken(), signVal);
        JSONObject data = responseVal.getJSONObject("data");
        return new ResultBean().error();
//        InstantShoppingDistributionNotifyDeliveryStatusRequest request = new InstantShoppingDistributionNotifyDeliveryStatusRequest();
//        InstantShoppingDistributionNotifyDeliveryStatusParam param = request.getParam();
//
//        param.setDeliveryId(orderId);
//
//        param.setDistributorCode("MERCHANT");
//        param.setDistributorDeliveryId(orderId);
//        param.setUpdateTime(new Date().getTime() / 1000);
//        param.setNotifyTime(new Date().getTime() / 1000);
//        param.setStatus(status);
//        if (!status.equals(101L)) {
//            param.setRiderName(username);
//            param.setRiderPhone(mobile);
//            param.setRiderLat(Double.valueOf(lat));
//            param.setRiderLng(Double.valueOf(lon));
//        }
//        InstantShoppingDistributionNotifyDeliveryStatusResponse response = request.execute(accessToken);
//        if (response.getCode().equals("10000")) {
//            return new ResultBean().success(response.getData());
//        } else {
//            return new ResultBean().error(response.getSubMsg());
//        }

    }

    @RequestMapping("getorder")
    public ResultBean getorder(String startTime,String endTime) throws Exception {

        GlobalConfig.initAppKey(appKey);
        GlobalConfig.initAppSecret(appSecret);
        //获取access_token方法
        AccessToken accessToken = AccessTokenBuilder.build(shopId);

        OrderSearchListRequest request = new OrderSearchListRequest();
        OrderSearchListParam param = request.getParam();
        List<Long> today = DateUtils.getToday();
//        param.setBType(2L);
        param.setSize(100L);
        param.setPage(0L);
        param.setCreateTimeStart(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startTime).getTime() / 1000);
        param.setCreateTimeEnd(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTime).getTime() / 1000);
        param.setOrderBy("create_time");
        param.setOrderAsc(false);
        OrderSearchListResponse response = request.execute(accessToken);

        if (response.getCode().equals("10000")) {
            OrderSearchListData data = response.getData();
            for (ShopOrderListItem shopOrderListItem : data.getShopOrderList()) {

                String order_id = shopOrderListItem.getOrderId();
                String mobile = gettoken(order_id, shopOrderListItem.getEncryptPostTel()); // 密文，电话
                String username = gettoken(order_id, shopOrderListItem.getEncryptPostReceiver()); // 密文，姓名
                String buyer_words = shopOrderListItem.getBuyerWords(); // 买家留言
                Long orderStatus = shopOrderListItem.getOrderStatus();
                Long pay_amount = shopOrderListItem.getPayAmount();
                Long pay_time = shopOrderListItem.getPayTime();
                PostAddr post_addr = shopOrderListItem.getPostAddr();
                String province = post_addr.getProvince().getName();
                String city = post_addr.getCity().getName();
                String town = post_addr.getTown().getName();
                String street = post_addr.getStreet().getName();
                String encrypt_detail = gettoken(order_id, post_addr.getEncryptDetail());
                Long storeId = null;
                if (shopOrderListItem.getUserCoordinate() != null) {
                    storeId = storeIdUtil.determineByWeiLan(
                        new BigDecimal(shopOrderListItem.getUserCoordinate().getUserCoordinateLatitude())
                    ,new BigDecimal(shopOrderListItem.getUserCoordinate().getUserCoordinateLongitude())
                    ,province, city, town, street,(town + encrypt_detail),0
                    );
                } else {
                    storeId = StoreIdUtil.determineStoreId(province, city, town, street);
                }
                Double orderTotalMoney = new BigDecimal(pay_amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();

                SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(order_id);

                if (null != szmCOrderMain) {
                    if (szmCOrderMain.getOrderStatus() >= 1) {
                        // 看看订单状态是不是取消了
                        if (orderStatus.equals(4L)) {
                            SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(order_id);
                            if (null == smzCOrderReturns) {
                                smzCOrderReturns = new SmzCOrderReturns();
                                smzCOrderReturns.setOrderReturnsDelStart(1);
                                smzCOrderReturns.setProcessstate(1);//退款状态
                                smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                smzCOrderReturns.setOrderDetailsId(order_id);
                                smzCOrderReturns.setReturnsType("退款");
                                smzCOrderReturns.setLogisticsDescription("其他");
                                smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                szmCOrderMain.setOrderStatus(8);
                                szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                
                                StoreMsg storeMsg = new StoreMsg();
                                storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                                storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                                storeMsg.setModelUrl("orderAdmin");//模块地址
                                storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                                StringBuffer stringBuffer = new StringBuffer();
                                stringBuffer.append("您的客户 ");
                                SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                    stringBuffer.append(szmCOrderMain.getUserName());
                                } else {
                                    stringBuffer.append(szmCUserinfo.getR3());
                                }
                                stringBuffer.append(" 于");
                                stringBuffer.append(DateUtil.formatDateTime(new Date()));
                                if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                    stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                } else {
                                    stringBuffer.append("发起了订单退款，请点击前往处理！");
                                }
                                storeMsg.setContent(stringBuffer.toString());//内容
                                storeMsg.setReadState(0);//已读 1 未读 0
                                storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                storeMsg.setDelState(0);//删除状态
                                storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                                storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                                storeMsgMapper.insert(storeMsg);
                            }
                        }
                    }
                    continue;
                }
                // 只有付过款的订单，才能进入
                if (!orderStatus.equals(105L) && !orderStatus.equals(2L)
                        && !orderStatus.equals(101L) && !orderStatus.equals(3L) && !orderStatus.equals(5L)) {
                    continue;
                }
                SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                if (null != szmCUser) {
                    // if (org.apache.commons.lang3.StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                        szmCUser.setR2(storeId.toString());
                        szmCUser.setStoreId(storeId);
                        szmCUser.setOrdersource(4);
                        szmCUserMapper.updateByPrimaryKey(szmCUser);
                    // }
                } else {
                    // 创建用户
                    szmCUser = new SzmCUser();
                    szmCUser.setUserMobile(mobile);
                    szmCUser.setUserNickname(username);
                    szmCUser.setR2(storeId.toString());
                    szmCUser.setStoreId(storeId);
                    szmCUser.setBindingTime(new Date());
                    szmCUser.setOrdersource(4);
                    ResultBean resultBean1 = szmCUserService.addUser(szmCUser);
                    if (1 != resultBean1.getCode()) {
                        return resultBean1;
                    }
                }

                SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                SzmCAddress szmCAddress = new SzmCAddress();
                szmCAddress.setUserId(szmCUserExtra.getUserId());
                szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
                szmCAddress.setProvince(province);
                szmCAddress.setCity(city);
                szmCAddress.setArea(town);
                szmCAddress.setStreet(street + encrypt_detail);
                szmCAddress.setIsDefaultAddress(0);
                szmCAddress.setState(0);
                if (shopOrderListItem.getUserCoordinate() != null) {
                    szmCAddress.setR5(shopOrderListItem.getUserCoordinate().getUserCoordinateLatitude() + "," + shopOrderListItem.getUserCoordinate().getUserCoordinateLongitude());
                }

                szmCAddressMapper.insertAddress(szmCAddress);
                // 开始组建订单
                
                //设置商品信息
                SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                Integer ordernum = 0;
                List<SkuOrderListItem> product = shopOrderListItem.getSkuOrderList();
                for (SkuOrderListItem skuOrderListItem : product) {
                    ordernum += skuOrderListItem.getItemNum().intValue();
                    szmCOrderMainlianying.setCompanyName(skuOrderListItem.getStoreInfo().getStoreId());
                }

                if (shopOrderListItem.getUserCoordinate() != null) {
                    szmCOrderMainlianying.setLat(new BigDecimal(shopOrderListItem.getUserCoordinate().getUserCoordinateLatitude()));
                    szmCOrderMainlianying.setLon(new BigDecimal(shopOrderListItem.getUserCoordinate().getUserCoordinateLongitude()));
                    szmCOrderMainlianying.setZuobiao(shopOrderListItem.getUserCoordinate().getUserCoordinateLatitude() + "," + shopOrderListItem.getUserCoordinate().getUserCoordinateLongitude());

                }
                szmCOrderMainlianying.setGroup(0);
                szmCOrderMainlianying.setUpPrice(0D);
                szmCOrderMainlianying.setRoyalty(0D);
                szmCOrderMainlianying.setRemind(0);
                szmCOrderMainlianying.setCdTypeMoney(0D);
                szmCOrderMainlianying.setCdMoneyType(0);
                szmCOrderMainlianying.setIsReturn(0);
                szmCOrderMainlianying.setIsSms(0);
                szmCOrderMainlianying.setIsForward(1);
                szmCOrderMainlianying.setIsInvoice(0);
                szmCOrderMainlianying.setBucketPrice(0D);
                szmCOrderMainlianying.setYfMoney(0D);
                szmCOrderMainlianying.setBack(0);
                szmCOrderMainlianying.setOrderDiscounts(0D);
                szmCOrderMainlianying.setFreightPayable(0D);
                szmCOrderMainlianying.setCdMoney(0D);
                szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                szmCOrderMainlianying.setCreateTime(new Date());
                szmCOrderMainlianying.setPayTime(new Date());
                szmCOrderMainlianying.setYuyuetime(StringUtils.isNotEmpty(shopOrderListItem.getReceiptDate()) ? 
                DateUtil.parse(shopOrderListItem.getReceiptDate(), "yyyy/MM/dd HH:mm") : null);
                szmCOrderMainlianying.setOrderNum(order_id);
                szmCOrderMainlianying.setUserName(username);
                szmCOrderMainlianying.setUserPhone(mobile);
                szmCOrderMainlianying.setUserAddress(province + city + town + street + encrypt_detail);
                szmCOrderMainlianying.setOrderMoney(0D);
                szmCOrderMainlianying.setR1("0");
                szmCOrderMainlianying.setPayNum(order_id);
                szmCOrderMainlianying.setOrderStatus(2);
                szmCOrderMainlianying.setIsReplenishment(0);
                szmCOrderMainlianying.setUserContent(buyer_words);
                szmCOrderMainlianying.setOrderDelState(0);
                szmCOrderMainlianying.setR3("0");
                szmCOrderMainlianying.setR4("[]");
                szmCOrderMainlianying.setBucketBeans("[]");
                szmCOrderMainlianying.setR5(ordernum.toString());
                szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                szmCOrderMainlianying.setPaymentModeId(8L);
                szmCOrderMainlianying.setDaike(0);
                szmCOrderMainlianying.setRemind(1);
                szmCOrderMainlianying.setOrdersource(4);
                
                // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                try {
                    OrderSource orderSource = orderSourceService.selectByPrimaryKey(4);
                    if (orderSource != null && orderSource.getSettlementCycle() != null) {
                        szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                    }
                } catch (Exception e) {
                    logger.error("获取订单来源结算周期失败", e);
                }
                
                szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                
                for (SkuOrderListItem skuOrderListItem : product) {
                    Long skuId = skuOrderListItem.getSkuId();

                    // 根据ordersource和skuId查询关联的product_id并计算价格
                    Double orderDetailsProductPrice = 0D;
                    String unitPrice = "0";
                    String totalPrice = "0";

                    try {
                        // 查询关联关系 (抖店的ordersource是4)
                        OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                            Long.valueOf(szmCOrderMainlianying.getOrdersource()), skuId.toString());

                        // 如果没有找到关联关系，尝试自动创建
                        if (connect == null) {
                            try {
                                connect = new OrderSourceConnect();
                                connect.setOrderSourceId(Long.valueOf(szmCOrderMainlianying.getOrdersource()));
                                connect.setUnionCode(skuId.toString());
                                connect.setName(skuOrderListItem.getProductName());
                                connect.setProductQuantity(1); // 设置默认件数为1
                                connect.setCreateTime(new Date());
                                connect.setUpdateTime(new Date());
                                orderSourceConnectMapper.insert(connect);
                                logger.info("抖店 - 自动新增关联关系：ordersource=" + szmCOrderMainlianying.getOrdersource() +
                                    ", skuId=" + skuId + ", name=" + connect.getName() + ", quantity=1");
                            } catch (Exception insertEx) {
                                logger.error("抖店 - 自动新增关联关系失败：ordersource=" + szmCOrderMainlianying.getOrdersource() +
                                    ", skuId=" + skuId, insertEx);
                            }
                        }

                        if (connect != null && connect.getProductNewId() != null) {
                            // 根据product_id查询产品价格
                            SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                            if (productInfo != null && productInfo.getPrice() != null) {
                                // 使用price字段而不是sellprice
                                BigDecimal basePrice = productInfo.getPrice();
                                Integer quantity = skuOrderListItem.getItemNum().intValue();

                                // 获取商品件数配置，默认为1
                                Integer productQuantity = connect.getProductQuantity() != null ? connect.getProductQuantity() : 1;

                                // 根据数量计算单位配送费
                                BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                // 单品最终单价 = 基础价格 + 单位配送费
                                BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                // 考虑商品件数：总价 = 单品最终单价 * 数量 * 商品件数
                                orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity))
                                    .multiply(new BigDecimal(productQuantity)).doubleValue();
                                unitPrice = finalUnitPrice.toString();
                                totalPrice = orderDetailsProductPrice.toString();

                                logger.info("抖店 - skuId: " + skuId + ", productId: " + connect.getProductNewId() +
                                    ", basePrice: " + basePrice + ", unitDeliveryFee: " + unitDeliveryFee +
                                    ", finalUnitPrice: " + finalUnitPrice + ", quantity: " + quantity +
                                    ", productQuantity: " + productQuantity + ", totalPrice: " + orderDetailsProductPrice);
                            } else {
                                logger.warn("抖店 - 关联关系存在但product_new_id为空或产品价格为空：ordersource=" + szmCOrderMainlianying.getOrdersource() +
                                    ", skuId=" + skuId + ", productNewId=" + (connect.getProductNewId() != null ? connect.getProductNewId() : "null"));
                            }
                        }
                    } catch (Exception e) {
                        logger.error("抖店 - 计算订单详情价格失败，skuId: " + skuId + ", ordersource: " + szmCOrderMainlianying.getOrdersource(), e);
                    }

                    SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                    smzCOrderDetailLianying.setSource(0);
                    smzCOrderDetailLianying.setProductModelId(null);
                    smzCOrderDetailLianying.setProductSkuname(skuOrderListItem.getProductName());
                    smzCOrderDetailLianying.setProductSkuimg(skuOrderListItem.getProductPic());
                    smzCOrderDetailLianying.setOrderProductNum(skuOrderListItem.getItemNum().intValue());
                    ordernum += skuOrderListItem.getItemNum().intValue();
                    smzCOrderDetailLianying.setR1(unitPrice);
                    smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                    smzCOrderDetailLianying.setOrderMainId(order_id);
                    smzCOrderDetailLianying.setR5(order_id + "1");//子订单编号
                    smzCOrderDetailLianying.setR4(totalPrice);
                    smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                    smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                    smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                    smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                }
                // 通知用户
                szmBOrderService.jieDanSms(order_id);
                // 通知商家
                StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                if (storeSmsInfo != null) {
                    SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                    if (storeSmsInfo.getResidueNum() > 0) {
                        SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                        if (smsRelevance != null && smsRelevance.getState() == 1) {
                            String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                            UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                            storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                            storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                            storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                            SmsRecord smsRecord = new SmsRecord();
                            smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                            SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                            smsRecord.setContent(smsMaster.getName());
                            smsRecordMapper.insert(smsRecord);
                        }
                    } else {
                        RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                    }
                }
                LoggerUtil.info("新增订单通知");
                StoreMsg storeMsg = new StoreMsg();
                storeMsg.setStoreMsgModel("新订单通知");//模块名称
                storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                storeMsg.setModelUrl("orderAdmin");//模块地址
                storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("您的客户 ");
                SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                if (Validator.isEmpty(szmCUserinfo.getR3())) {
                    stringBuffer.append(szmCOrderMainlianying.getUserName());
                } else {
                    stringBuffer.append(szmCUserinfo.getR3());
                }
                stringBuffer.append(" 于");
                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                stringBuffer.append("下了一笔订单，请点击前往处理！");
                storeMsg.setContent(stringBuffer.toString());//内容
                storeMsg.setReadState(0);//已读 1 未读 0
                storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                storeMsg.setDelState(0);//删除状态
                storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                storeMsgMapper.insert(storeMsg);
                try {
                    // 自动派单送水员
                    Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                    szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                    logger.error("送水员围栏判断，找到送水员id:{}", deliveryUserId);
                if (deliveryUserId != null) {
                        szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                    }
                } catch (Exception e) {
                    logger.error("自动派单送水员失败", e);
                }
            }
        } else {
            return new ResultBean().error(response.getMsg());
        }

        return new ResultBean().success(response.getData());
    }

    @RequestMapping("gettoken")
    public String gettoken(String orderId, String result) throws Exception {
        String douyintoken = (String) redisUtil.get("douyintoken");
        if (StringUtils.isNotEmpty(douyintoken)) {
            return douyintoken;
        }
        //设置appKey和appSecret，全局设置一次

        GlobalConfig.initAppKey(appKey);

        GlobalConfig.initAppSecret(appSecret);

        //获取access_token方法
        AccessToken accessToken = AccessTokenBuilder.build(shopId); //入参为shopId
        // 收集参数
        String host = "https://openapi-fxg.jinritemai.com";
        String method = "order.batchDecrypt";

        long timestamp = System.currentTimeMillis() / 1000;

        Map<String, Object> m2 = new HashMap<>();
        m2.put("auth_id", orderId);
        m2.put("cipher_text", result);

        Map<String, Object> m = new HashMap<>();
        m.put("cipher_infos", new Object[]{m2});

        // 序列化参数
        String paramJson = marshal(m);
        // 计算签名
        String signVal = sign(appKey, appSecret, method, timestamp, paramJson);
        // 发起请求
        JSONObject responseVal = fetch(appKey, host, method, timestamp, paramJson, accessToken.getAccessToken(), signVal);
        JSONObject data = responseVal.getJSONObject("data");
        if (null != data) {
            JSONArray decryptInfos = data.getJSONArray("decrypt_infos");
            if (null != decryptInfos && decryptInfos.size() > 0) {
                JSONObject token = (JSONObject) decryptInfos.get(0);
                if (null != token) {
                    return token.getString("decrypt_text");
                }

            }

        }

        return null;

    }

    // 序列化参数
// 这一步看上去冗余，实际很重要。如果要自己实现，则必须保证这三点：
// 1、保证JSON所有层级上Key的有序性
// 2、保证JSON的所有数值不带多余的小数点
// 3、保证转义逻辑与这段代码一致
    public static String marshal(Object o) {
        String raw = CustomGson.toJson(o);
        Map<?, ?> m = CustomGson.fromJson(raw, LinkedTreeMap.class); // 执行反序列化，把所有JSON对象转换成LinkedTreeMap
        return CustomGson.toJson(m); // 重新序列化，保证JSON所有层级上Key的有序性
    }

    private static final Gson CustomGson = new GsonBuilder()
            .registerTypeAdapter(LinkedTreeMap.class, newMapSerializer()) // 定制LinkedTreeMap序列化，确保所有key按字典序排序
            .registerTypeAdapter(Integer.class, newNumberSerializer()) // 定制数值类型的序列化，确保整数输出不带小数点
            .registerTypeAdapter(Long.class, newNumberSerializer()) // 同上
            .registerTypeAdapter(Double.class, newNumberSerializer()) // 同上
            .disableHtmlEscaping() // 禁用Html Escape，确保符号不转义：&<>='
            .create();

    // 为LinkedTreeMap定制的序列化器
    public static JsonSerializer<LinkedTreeMap<?, ?>> newMapSerializer() {
        return new JsonSerializer<LinkedTreeMap<?, ?>>() {
            @Override
            public JsonElement serialize(LinkedTreeMap<?, ?> src, Type typeOfSrc, JsonSerializationContext context) {
                List<String> keys = src.keySet().stream().map(Object::toString).sorted().collect(Collectors.toList());
                JsonObject o = new JsonObject();
                for (String k : keys) {
                    o.add(k, context.serialize(src.get(k)));
                }
                return o;
            }
        };
    }

    // 为Number定制化的序列化器
    private static <T extends Number> JsonSerializer<T> newNumberSerializer() {
        return new JsonSerializer<T>() {
            @Override
            public JsonElement serialize(T number, Type type, JsonSerializationContext context) {
                if (number instanceof Integer) {
                    return new JsonPrimitive(number.intValue());
                }
                if (number instanceof Long) {
                    return new JsonPrimitive(number.longValue());
                }
                if (number instanceof Double) {
                    long longValue = number.longValue();
                    double doubleValue = number.doubleValue();
                    if (longValue == doubleValue) {
                        return new JsonPrimitive(longValue);
                    }
                }
                return new JsonPrimitive(number);
            }
        };
    }

    // 计算签名
    public static String sign(String appKey, String appSecret, String method, long timestamp, String paramJson) {
        // 按给定规则拼接参数
        String paramPattern = "app_key" + appKey + "method" + method + "param_json" + paramJson + "timestamp" + timestamp + "v2";
        String signPattern = appSecret + paramPattern + appSecret;
        System.out.println("sign_pattern:" + signPattern);

        return hmac(signPattern, appSecret);
    }

    // 计算hmac
    public static String hmac(String plainText, String appSecret) {
        Mac mac;
        try {
            byte[] secret = appSecret.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec keySpec = new SecretKeySpec(secret, "HmacSHA256");

            mac = Mac.getInstance("HmacSHA256");
            mac.init(keySpec);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            return "";
        }

        byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);
        byte[] digest = mac.doFinal(plainBytes);
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    // 调用Open Api，取回数据
    public static JSONObject fetch(String appKey, String host, String method, long timestamp, String paramJson, String accessToken, String sign) throws Exception {
        String methodPath = method.replace('.', '/');
        Map<String, Object> params = new HashMap<>();
        String s = HttpUtil.sendSimplePostRequest(host, params);
        String u = host + "/" + methodPath +
                "?method=" + URLEncoder.encode(method, "UTF-8") +
                "&app_key=" + URLEncoder.encode(appKey, "UTF-8") +
                "&access_token=" + URLEncoder.encode(accessToken, "UTF-8") +
                "&timestamp=" + URLEncoder.encode(Long.toString(timestamp), "UTF-8") +
                "&v=" + URLEncoder.encode("2", "UTF-8") +
                "&sign=" + URLEncoder.encode(sign, "UTF-8") +
                "&sign_method=" + URLEncoder.encode("hmac-sha256", "UTF-8");

        URL url = new URL(u);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Accept", "*/*");
        conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");

        OutputStream os = conn.getOutputStream();
        os.write(paramJson.getBytes("UTF-8"));
        os.flush();
        int code = conn.getResponseCode();
        if (code == 200) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            InputStream inputStream = conn.getInputStream();
            byte[] in = new byte[1024];
            int len;
            while ((len = inputStream.read(in)) > -1) {
                baos.write(in, 0, len);
            }
            String content = baos.toString("UTF-8");
            baos.close();
            inputStream.close();
            conn.disconnect();

            JSONObject jsonObject = JSON.parseObject(content);
            // 根据定义好的数据结构解析出想要的东西
            return jsonObject;
        } else {
            return null;
        }
    }

    /**
     * 根据deliveryfee配置和数量计算配送费
     * @param deliveryFeeJson deliveryfee字段的JSON字符串，格式：[{"max":1,"fee":"9.5"},{"max":9,"fee":"9"},{"max":null,"fee":"8"}]
     * @param quantity 商品数量
     * @return 配送费
     */
    private BigDecimal calculateDeliveryFee(String deliveryFeeJson, Integer quantity) {
        if (StringUtils.isEmpty(deliveryFeeJson) || quantity == null || quantity <= 0) {
            return BigDecimal.ZERO;
        }

        try {
            JSONArray deliveryFeeArray = JSON.parseArray(deliveryFeeJson);
            if (deliveryFeeArray == null || deliveryFeeArray.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 遍历配送费配置，找到匹配的区间
            for (Object item : deliveryFeeArray) {
                JSONObject feeConfig = (JSONObject) item;
                Object maxObj = feeConfig.get("max");
                String feeStr = feeConfig.getString("fee");

                if (StringUtils.isEmpty(feeStr)) {
                    continue;
                }

                // 如果max为null，表示无上限，直接返回该费率
                if (maxObj == null) {
                    return new BigDecimal(feeStr);
                }

                // 如果数量小于等于max值，返回该费率
                Integer maxValue = Integer.valueOf(maxObj.toString());
                if (quantity <= maxValue) {
                    return new BigDecimal(feeStr);
                }
            }

            // 如果没有找到匹配的区间，返回0
            return BigDecimal.ZERO;

        } catch (Exception e) {
            logger.error("解析配送费配置失败，deliveryFeeJson: " + deliveryFeeJson + ", quantity: " + quantity, e);
            return BigDecimal.ZERO;
        }
    }
}