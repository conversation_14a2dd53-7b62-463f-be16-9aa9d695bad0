package com.example.waterstationbuyproducer.szmb.newController;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.ApiExecutor;
import com.alibaba.ocean.rawsdk.client.pollicy.RequestPolicy;
import com.alibaba.ocean.rawsdk.common.BizResultWrapper;
import com.alibaba.ocean.rawsdk.example.SdkExample;
import com.alibaba.ocean.rawsdk.example.param.CBCRequestBody;
import com.alibaba.ocean.rawsdk.example.param.CommonBusinessCatParam;
import com.alibaba.ocean.rawsdk.example.param.CommonBusinessCatResult;
import com.doudian.open.api.instantShopping_getDeliveryListByOrderId.param.InstantShoppingGetDeliveryListByOrderIdParam;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.entity.*;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmb.service.order.OrderSourceService;
import com.example.waterstationbuyproducer.szmc.service.SzmCUserService;
import com.example.waterstationbuyproducer.util.CoordinateTransformUtil;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.util.StoreIdUtil;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.rabbit.RabbitMqUtil;
import com.example.waterstationbuyproducer.util.sms.RemindSMS;
import com.example.waterstationbuyproducer.util.sms.UtilSMS;
import com.pdd.pop.sdk.http.api.pop.response.PddDdkAllOrderListIncrementGetResponse.OrderListGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddDdkAllOrderListIncrementGetResponse.OrderListGetResponseOrderListItem;

import me.ele.retail.param.*;
import me.ele.retail.param.model.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @describe:
 * @program: shuizhanmai-old-master
 * @author: cjy
 * @create: 2024-08-12 23:20
 */
@RestController
@RequestMapping("elementorder")
public class ElementOrderController {
    protected static final Log logger = LogFactory.getLog(ElementOrderController.class);

    private static final String SUCCESS_FLAG = "0";

    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCUserService szmCUserService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;
    
    @Autowired
    private OrderSourceService orderSourceService;


    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;

    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;

    @Autowired
    private SmsMasterMapper smsMasterMapper;

    @Autowired
    private SmsRecordMapper smsRecordMapper;

    @Autowired
    private StoreMsgMapper storeMsgMapper;

    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;

    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;

    @Autowired
    private RabbitMqUtil rabbitMqUtil;
    @Autowired
    private StoreIdUtil storeIdUtil;
    @Autowired
    private SmzCDeliveryInfoMapper smzCDeliveryInfoMapper;

    @Autowired
    private OrderSourceConnectMapper orderSourceConnectMapper;

    @Autowired
    private SzmCProductNewMapper szmCProductNewMapper;

        String appkey = "40828103";
    String secKey = "h59jAH8aDO";
//    String appkey = "31659062";
//    String secKey = "S8bHUY55goB";



    @RequestMapping("refreshPhone")
    public ResultBean refreshPhone(String orderNum) {

        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNum);
        if (null == szmCOrderMain) {
            return new ResultBean();
        }
        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderPrivateinfoParam param = new OrderPrivateinfoParam();
        //业务请求参数
        MeEleNopDoaApiParamRequestOrderOrderCmdBaseReqDtoOrderId body = new MeEleNopDoaApiParamRequestOrderOrderCmdBaseReqDtoOrderId();
        body.setOrder_id(orderNum);

        param.setBody(body);
        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderPrivateinfoResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderPrivateinfoResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                MeEleNopDoaApiDtoOrderPrivateinfoOrderPrivateinfoDataResultDataDto data = commonBusinessCatResult.getData();
                if (StringUtils.isNotEmpty(data.getShort_number())) {

                    szmCOrderMain.setUserPhone(data.getShort_number());
                    szmCOrderMainMapper.updateByOrderNum(szmCOrderMain);
                }

                return new ResultBean().success(data.getShort_number());
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }
    @RequestMapping("selfDeliveryStateSync")
    public ResultBean selfDeliveryStateSync(String orderId,Integer state, String name, String mobile) {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderSelfDeliveryStateSyncParam param = new OrderSelfDeliveryStateSyncParam();
        //业务请求参数
        MeEleNopDoaApiParamRequestOrderOrderStateSyncReqDto body = new MeEleNopDoaApiParamRequestOrderOrderStateSyncReqDto();
        body.setOrder_id(orderId);
        body.setSelfStatus(state);
        param.setBody(body);
        if (StringUtils.isNotEmpty(name)) {

            MeEleNopDoaApiParamRequestOrderKnightReqDto knightReqDto = new MeEleNopDoaApiParamRequestOrderKnightReqDto();
            knightReqDto.setName(name);
            knightReqDto.setName(mobile);
            body.setKnight(knightReqDto);
        }
        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderSelfDeliveryStateSyncResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderSelfDeliveryStateSyncResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                Object data = commonBusinessCatResult.getData();

                return new ResultBean().success(data);
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }

    @RequestMapping("notify")
    public Map<String, Object> notify(@RequestParam Map<String, Object> params) {
        logger.error("params:" + JSON.toJSONString(params));
        String cmd = (String) params.get("cmd");
        logger.error("cmd:" + (String) params.get("cmd"));
        if (StringUtils.isNotEmpty(cmd) && cmd.equals("order.create")) {
            // 创建订单的逻辑
            JSONObject jsonObject = JSON.parseObject((String) params.get("body"));
            String orderId = jsonObject.getString("order_id");
            return this.getOrder(orderId);
        }
        // if (StringUtils.isNotEmpty(cmd) && cmd.equals("order.user.cancel")) {
        //     // 创建订单的逻辑
        //     JSONObject jsonObject = JSON.parseObject((String) params.get("body"));
        //     String orderId = jsonObject.getString("order_id");
        //     String refund_order_id = jsonObject.getString("refund_order_id");
        //     return this.cancelOrder(orderId,refund_order_id);
        // }
        if (StringUtils.isNotEmpty(cmd) && cmd.equals("order.reverse.push")) {
            // 创建订单的逻辑
            JSONObject jsonObject = JSON.parseObject((String) params.get("body"));
            String orderId = jsonObject.getString("order_id");
            String refund_order_id = jsonObject.getString("refund_order_id");
            JSONObject cur_reverse_event = jsonObject.getJSONObject("cur_reverse_event");
            Integer statusNodeal = cur_reverse_event == null ? 0 : cur_reverse_event.getInteger("refund_status");
            // statusNodeal.equals(40) || 
            Integer status = (statusNodeal.equals(0) || statusNodeal.equals(10)) ? 0 : (statusNodeal.equals(50)) ? 1 : 2;
            return this.cancelOrderReverse(orderId,refund_order_id,status);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("error", "success");
        return result;
    }

    @RequestMapping("notify1")
    public void notify(@RequestBody String params) {
        logger.error("params:" + params);
    }

    @RequestMapping("cancelOrder")
    public Map<String, Object> cancelOrder(String orderId,String refund_order_id) {
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
        if (null != szmCOrderMain) {
            if (szmCOrderMain.getOrderStatus() >= 1) {
                // 看看订单状态是不是取消了
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId);
                    if (null == smzCOrderReturns) {
                        smzCOrderReturns = new SmzCOrderReturns();
                        smzCOrderReturns.setOrderReturnsDelStart(1);
                        smzCOrderReturns.setProcessstate(0);//退款状态
                        smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                        smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                        smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                        smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                        smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                        smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                        smzCOrderReturns.setR5(refund_order_id);
                        smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                        smzCOrderReturns.setOrderDetailsId(orderId);
                        smzCOrderReturns.setReturnsType("退款");
                        smzCOrderReturns.setLogisticsDescription("其他");
                        smzCOrderReturnsMapper.insert(smzCOrderReturns);
                        szmCOrderMain.setOrderStatus(8);
                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                        
                                        
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                        storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMain.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(DateUtil.formatDateTime(szmCOrderMain.getCreateTime()));
                        if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                            stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                        } else {
                            stringBuffer.append("发起了订单退款，请点击前往处理！");
                        }
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                        storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                        storeMsgMapper.insert(storeMsg);
                    }
                }
                Map<String, Object> result = new HashMap<>();
                result.put("code", 0);
                result.put("error", "success");
                return result;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("error", "订单不存在");
        return result;
    }

    @RequestMapping("cancelOrderReverse")
    public Map<String, Object> cancelOrderReverse(String orderId,String refund_order_id,Integer status) {
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
        if (null != szmCOrderMain) {
            // if (szmCOrderMain.getOrderStatus() >= 1) {
                // 看看订单状态是不是取消了
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId);
                    if (null == smzCOrderReturns) {
                        smzCOrderReturns = new SmzCOrderReturns();
                        smzCOrderReturns.setOrderReturnsDelStart(1);
                        smzCOrderReturns.setProcessstate(status);//退款状态
                        smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                        smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                        smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                        smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                        smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                        smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                        smzCOrderReturns.setR5(refund_order_id);
                        smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                        smzCOrderReturns.setOrderDetailsId(orderId);
                        smzCOrderReturns.setReturnsType("退款");
                        smzCOrderReturns.setLogisticsDescription("其他");
                        smzCOrderReturnsMapper.insert(smzCOrderReturns);
                        if (status.equals(2)) {
                            
                            SmzCDeliveryInfo smzCDeliveryInfo = smzCDeliveryInfoMapper.selectAllByOrderId(szmCOrderMain.getOrderMainId());
                            if (null == smzCDeliveryInfo) {
                                szmCOrderMain.setOrderStatus(2);
                                
                            } else {
                                if (smzCDeliveryInfo.getDeliveryInfoState() != null && smzCDeliveryInfo.getDeliveryInfoState().equals(0)) {
                                    
                                szmCOrderMain.setOrderStatus(3);
                                } else {
                                    szmCOrderMain.setOrderStatus(5);

                                }
                            }
                        } else {
                            szmCOrderMain.setOrderStatus( 8);

                        }
                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                        
                                        
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                        storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMain.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(DateUtil.formatDateTime(szmCOrderMain.getCreateTime()));
                        if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                            stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                        } else {
                            stringBuffer.append("发起了订单退款，请点击前往处理！");
                        }
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                        storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                        storeMsgMapper.insert(storeMsg);
                    } else {
                        smzCOrderReturns.setR5(refund_order_id);
                        smzCOrderReturns.setProcessstate(status);
                        smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                        if (status == 2) {
                            SmzCDeliveryInfo smzCDeliveryInfo = smzCDeliveryInfoMapper.selectAllByOrderId(szmCOrderMain.getOrderMainId());
                            if (null == smzCDeliveryInfo) {
                                szmCOrderMain.setOrderStatus(2);
                                
                            } else {
                                if (smzCDeliveryInfo.getDeliveryInfoState() != null && smzCDeliveryInfo.getDeliveryInfoState().equals(0)) {
                                    
                                szmCOrderMain.setOrderStatus(3);
                                } else {
                                    szmCOrderMain.setOrderStatus(5);

                                }
                            }
                        } else {
                            szmCOrderMain.setOrderStatus(8);
                        }
                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                    }
                // }

            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("error", "success");
            return result;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("error", "订单不存在");
        return result;
    }

    @RequestMapping("orderList")
    public void orderList(String start,String end,Integer pageNo) {
        int pageNow = pageNo == null ? 1 : pageNo;
        // order.list
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderListParam param = new OrderListParam();
        // 业务请求参数
        MeEleNopDoaApiParamRequestOrderOrderListReqDto body = new MeEleNopDoaApiParamRequestOrderOrderListReqDto();
        // start,end 转时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date startDate = sdf.parse(start);
            Date endDate = sdf.parse(end);
            body.setStart_time((String.valueOf(startDate.getTime() / 1000)));
            body.setEnd_time((String.valueOf(endDate.getTime() / 1000)));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        body.setPage(pageNow);
        body.setPage_size(10);
        param.setBody(body);
        param.setTicket(UUID.randomUUID().toString().toUpperCase());
        
        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderListResult> result1 = apiExecutor.send(param);
            if (null == result1 || null == result1.getBody()) {
                logger.error("返回数据中对象为空");
            }
            OrderListResult orderListGetResponse = result1.getBody();
            if (orderListGetResponse.getData() == null) {
                logger.error("返回数据中对象为空");
            }
            MeEleNopDoaApiDtoOrderListOrderBasicInfoDto[] orderList = orderListGetResponse.getData().getList();
            if (orderList.length == 0) {
                return;
            }
            for (MeEleNopDoaApiDtoOrderListOrderBasicInfoDto order : orderList) {
                getOrder(order.getOrder_id());
            }
            pageNow = pageNow + 1;
            this.orderList(start, end, pageNow);
        } catch (Exception e) {
            logger.error("返回数据中对象为空");
        }
    }

    @RequestMapping("getOrder")
    public Map<String, Object> getOrder(String orderId) {
        // 判断订单是否在库里面
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
        Map<String, Object> result = new HashMap<>();
        //业务请求参数
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderGetParam param = new OrderGetParam();
        //业务请求参数
        MeEleRetailOrderGetReqDto body = new MeEleRetailOrderGetReqDto();
        body.setOrder_id(orderId);
        param.setBody(body);

        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderGetResult> result1 = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result1));
            if (null == result1 || null == result1.getBody()) {
                logger.error("返回数据中对象为空");
                result.put("code", 1);
                result.put("error", "返回数据中对象为空");
                return result;
            }
            OrderGetResult commonBusinessCatResult = result1.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                Integer orderNum = 0;
                MeEleNopDoaApiDtoOrderGetOrderGetDataResultDataDto data = commonBusinessCatResult.getData();
                MeEleNopDoaApiDtoOrderGetOrder order = data.getOrder();
                MeEleNopDoaApiDtoOrderGetUser user = data.getUser();
                Double latitude  = null;
                Double longitude  = null;
                if (user.getCoord() != null && user.getCoord().getLatitude() != null && !user.getCoord().getLatitude().equals("**")) {
                    // 坐标转换
                    Double latitude1 =  new BigDecimal((String)user.getCoord().getLatitude()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    Double longitude1 =  new BigDecimal((String)user.getCoord().getLongitude()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    if (latitude1 != null && longitude1 != null) {
                        double[] coordinate = CoordinateTransformUtil.bd09ToGcj02(latitude1, longitude1);
                        if (coordinate != null && coordinate.length == 2) {
                            latitude = coordinate[0];
                            longitude = coordinate[1];
                        }
                    }
                }
                Long storeId = null;
                if (latitude != null && longitude != null) {
                    storeId = storeIdUtil.determineByWeiLan(new BigDecimal(longitude),new BigDecimal(latitude),  user.getProvince(),user.getCity(), user.getDistrict(),user.getAddress(), user.getAddress(),0);
                } else {
                    storeId = StoreIdUtil.determineStoreId(user.getProvince(), user.getCity(), user.getDistrict(),user.getAddress());
                }

                if (null != szmCOrderMain) {
                    result.put("code", 0);
                    result.put("error", "success");
                    return result;
                }
                SzmCUser szmCUser = szmCUserMapper.selectByPhone(user.getPhone());
                if (null != szmCUser) {
                    // if (org.apache.commons.lang3.StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                        szmCUser.setR2(storeId.toString());
                        szmCUser.setStoreId(storeId);
                        szmCUser.setOrdersource(2);
                        szmCUserMapper.updateByPrimaryKey(szmCUser);
                    // }
                } else {
                    // 创建用户
                    szmCUser = new SzmCUser();
                    szmCUser.setUserMobile(user.getPhone());
                    szmCUser.setUserNickname(user.getName());
                    szmCUser.setR2(storeId.toString());
                    szmCUser.setStoreId(storeId);
                    szmCUser.setBindingTime(new Date());
                    szmCUser.setOrdersource(2);
                    szmCUserService.addUser(szmCUser);
                }

                SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(user.getPhone());

                SzmCAddress szmCAddress = new SzmCAddress();
                szmCAddress.setUserId(szmCUserExtra.getUserId());
                szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
                szmCAddress.setCity(user.getCity());
                szmCAddress.setArea(user.getDistrict());
                szmCAddress.setStreet(user.getAddress());
                szmCAddress.setIsDefaultAddress(0);
                szmCAddress.setState(0);
                szmCAddress.setR1("0");
                szmCAddress.setR2("1");
                if (latitude != null && longitude != null) {
                    szmCAddress.setR5(latitude + "," + longitude);
                }
                szmCAddressMapper.insertAddress(szmCAddress);

                for (MeEleNopDoaApiDtoOrderGetProduct object : data.getProducts()[0]) {

                    orderNum += object.getProduct_amount();
                }

                //设置商品信息
                SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                szmCOrderMainlianying.setAppkey(appkey);
                szmCOrderMainlianying.setApptoken(secKey);
                if (latitude != null && longitude != null) {
                    szmCOrderMainlianying.setZuobiao(latitude + "," + longitude);
                    szmCOrderMainlianying.setLat(new BigDecimal(latitude));
                    szmCOrderMainlianying.setLon(new BigDecimal(longitude));
                }

                szmCOrderMainlianying.setGroup(0);
                szmCOrderMainlianying.setUpPrice(0D);
                szmCOrderMainlianying.setRoyalty(0D);
                szmCOrderMainlianying.setRemind(0);
                szmCOrderMainlianying.setCdTypeMoney(0D);
                szmCOrderMainlianying.setCdMoneyType(0);
                szmCOrderMainlianying.setIsReturn(0);
                szmCOrderMainlianying.setIsSms(0);
                szmCOrderMainlianying.setIsForward(1);
                szmCOrderMainlianying.setIsInvoice(0);
                szmCOrderMainlianying.setBucketPrice(0D);
                szmCOrderMainlianying.setYfMoney(0D);
                szmCOrderMainlianying.setBack(0);
                szmCOrderMainlianying.setOrderDiscounts(0D);
                szmCOrderMainlianying.setFreightPayable(0D);
                szmCOrderMainlianying.setCdMoney(0D);
                szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                szmCOrderMainlianying.setCreateTime(new Date());
                szmCOrderMainlianying.setPayTime(new Date());
                szmCOrderMainlianying.setOrderNum(orderId);
                szmCOrderMainlianying.setUserName(user.getName());
                szmCOrderMainlianying.setUserPhone(user.getPhone());
                szmCOrderMainlianying.setUserAddress(user.getProvince() + user.getCity() + user.getDistrict() + user.getAddress());
                szmCOrderMainlianying.setOrderMoney(0D);
                szmCOrderMainlianying.setR1("0");
                szmCOrderMainlianying.setPayNum(orderId);
                szmCOrderMainlianying.setOrderStatus(2);
                szmCOrderMainlianying.setIsReplenishment(0);
                szmCOrderMainlianying.setUserContent(order.getRemark());
                if (null != order.getLatest_send_time()) {
                    szmCOrderMainlianying.setYuyuetime(new Date(order.getLatest_send_time() * 1000));
                }

                szmCOrderMainlianying.setOrderDelState(0);
                szmCOrderMainlianying.setR3("0");
                szmCOrderMainlianying.setR4("[]");
                szmCOrderMainlianying.setBucketBeans("[]");
                szmCOrderMainlianying.setR5(orderNum.toString());
                szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                szmCOrderMainlianying.setPaymentModeId(8L);
                szmCOrderMainlianying.setDaike(0);
                szmCOrderMainlianying.setRemind(1);
                szmCOrderMainlianying.setOrdersource(2);
                
                // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                try {
                    OrderSource orderSource = orderSourceService.selectByPrimaryKey(2);
                    if (orderSource != null && orderSource.getSettlementCycle() != null) {
                        szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                    }
                } catch (Exception e) {
                    logger.error("获取订单来源结算周期失败", e);
                }
                
                szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                
                for (MeEleNopDoaApiDtoOrderGetProduct object : data.getProducts()[0]) {
                    String product_id = object.getBaidu_product_id();

                    // 根据ordersource和product_id查询关联的product_id并计算价格
                    Double orderDetailsProductPrice = 0D;
                    String unitPrice = "0";
                    String totalPrice = "0";

                    try {
                        // 查询关联关系 (饿了么的ordersource是2)
                        OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(Long.valueOf(szmCOrderMainlianying.getOrdersource()), product_id);

                        // 如果没有找到关联关系，尝试自动创建
                        if (connect == null) {
                            try {
                                connect = new OrderSourceConnect();
                                connect.setOrderSourceId(2L);
                                connect.setUnionCode(product_id);
                                connect.setName(object.getProduct_name());
                                connect.setProductQuantity(1); // 设置默认件数为1
                                connect.setCreateTime(new Date());
                                connect.setUpdateTime(new Date());
                                orderSourceConnectMapper.insert(connect);
                                logger.info("饿了么 - 自动新增关联关系：ordersource=2, product_id=" + product_id + ", name=" + connect.getName() + ", quantity=1");
                            } catch (Exception insertEx) {
                                logger.error("饿了么 - 自动新增关联关系失败：ordersource=2, product_id=" + product_id, insertEx);
                            }
                        }

                        if (connect != null && connect.getProductNewId() != null) {
                            // 根据product_id查询产品价格
                            SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                            if (productInfo != null && productInfo.getPrice() != null) {
                                // 使用price字段而不是sellprice
                                BigDecimal basePrice = productInfo.getPrice();
                                Integer quantity = object.getProduct_amount();

                                // 获取商品件数配置，默认为1
                                Integer productQuantity = connect.getProductQuantity() != null ? connect.getProductQuantity() : 1;

                                // 根据数量计算单位配送费
                                BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                // 单品最终单价 = 基础价格 + 单位配送费
                                BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                // 考虑商品件数：总价 = 单品最终单价 * 数量 * 商品件数
                                orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity))
                                    .multiply(new BigDecimal(productQuantity)).doubleValue();
                                unitPrice = finalUnitPrice.toString();
                                totalPrice = orderDetailsProductPrice.toString();

                                logger.info("饿了么 - product_id: " + product_id + ", productId: " + connect.getProductNewId() +
                                    ", basePrice: " + basePrice + ", unitDeliveryFee: " + unitDeliveryFee +
                                    ", finalUnitPrice: " + finalUnitPrice + ", quantity: " + quantity +
                                    ", productQuantity: " + productQuantity + ", totalPrice: " + orderDetailsProductPrice);
                            } else {
                                logger.warn("饿了么 - 关联关系存在但product_new_id为空或产品价格为空：ordersource=2, product_id=" + product_id +
                                    ", productNewId=" + (connect.getProductNewId() != null ? connect.getProductNewId() : "null"));
                            }
                        }
                    } catch (Exception e) {
                        logger.error("饿了么 - 计算订单详情价格失败，product_id: " + product_id + ", ordersource: 2", e);
                    }

                    SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                    smzCOrderDetailLianying.setSource(0);
                    smzCOrderDetailLianying.setProductModelId(null);
                    smzCOrderDetailLianying.setProductSkuname(object.getProduct_name());
                    smzCOrderDetailLianying.setProductSkuimg("");
                    smzCOrderDetailLianying.setOrderProductNum(object.getProduct_amount());
                    orderNum += object.getProduct_amount();
                    smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                    smzCOrderDetailLianying.setR1(unitPrice);
                    smzCOrderDetailLianying.setOrderMainId(orderId);
                    smzCOrderDetailLianying.setR5(orderId + "1");//子订单编号
                    smzCOrderDetailLianying.setR4(totalPrice);
                    smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                    smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                    smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                    smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                }
                // 通知用户
                szmBOrderService.jieDanSms(orderId);
                // 通知商家
                StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                if (storeSmsInfo != null) {
                    SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                    if (storeSmsInfo.getResidueNum() > 0) {
                        SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                        if (smsRelevance != null && smsRelevance.getState() == 1) {
                            String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                            UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                            storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                            storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                            storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                            SmsRecord smsRecord = new SmsRecord();
                            smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                            SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                            smsRecord.setContent(smsMaster.getName());
                            smsRecordMapper.insert(smsRecord);
                        }
                    } else {
                        RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                    }
                }
                LoggerUtil.info("新增订单通知");
                StoreMsg storeMsg = new StoreMsg();
                storeMsg.setStoreMsgModel("新订单通知");//模块名称
                storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                storeMsg.setModelUrl("orderAdmin");//模块地址
                storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("您的客户 ");
                SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                if (Validator.isEmpty(szmCUserinfo.getR3())) {
                    stringBuffer.append(szmCOrderMainlianying.getUserName());
                } else {
                    stringBuffer.append(szmCUserinfo.getR3());
                }
                stringBuffer.append(" 于");
                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                stringBuffer.append("下了一笔订单，请点击前往处理！");
                storeMsg.setContent(stringBuffer.toString());//内容
                storeMsg.setReadState(0);//已读 1 未读 0
                storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                storeMsg.setDelState(0);//删除状态
                storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                storeMsgMapper.insert(storeMsg);
                this.confirm(orderId);
                result.put("code", 0);
                result.put("error", "success");
                Map<String,Object> map2 = new HashMap<>();
                map2.put("source_order_id", orderId);
                result.put("data", map2);

                
                try {
                    // 自动派单送水员
                    Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                    szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                    logger.error("送水员围栏判断，找到送水员id:"+ deliveryUserId);
                if (deliveryUserId != null) {
                        szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                    }
                } catch (Exception e) {
                    logger.error("自动派单送水员失败", e);
                }
                return result;
            } else {
                result.put("code", 1);
                result.put("error", commonBusinessCatResult.getError());
                return result;
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        result.put("code", 0);
        result.put("error", "success");

        return result;
    }


    @RequestMapping("confirm")
    public ResultBean confirm(String orderId) {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderConfirmParam param = new OrderConfirmParam();
        //业务请求参数
        MeEleRetailOrderConfirmInputParam body = new MeEleRetailOrderConfirmInputParam();
        body.setOrder_id(orderId);
        param.setBody(body);

        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderConfirmResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderConfirmResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                Object data = commonBusinessCatResult.getData();
                rabbitMqUtil.convertAndSend("test_exchange101", "test_queue_101", orderId, 300L);
                return new ResultBean().success(data);
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }

    @RequestMapping("OrderReverseReviewReqDTO")
    public ResultBean OrderReverseReviewReqDTO(String orderId,String refund_order_id,String state) {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderReverseProcessParam param = new OrderReverseProcessParam();
        MeEleNewretailOrderApiClientModelReqOrderReverseReviewReqDTO body = new MeEleNewretailOrderApiClientModelReqOrderReverseReviewReqDTO();
        //业务请求参数
        body.setOrder_id(orderId);
        body.setReverse_order_id(refund_order_id);
        body.setIdempotent_id(UUID.randomUUID().toString().toUpperCase());
        body.setAction_type(state);
        param.setBody(body);
        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderReverseProcessResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderReverseProcessResult commonBusinessCatResult = result.getBody();;
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                return new ResultBean().success();
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }

    @RequestMapping("pickcomplete")
    public ResultBean pickcomplete(String orderId) {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderPickcompleteParam param = new OrderPickcompleteParam();
        //业务请求参数
        MeEleRetailOrderPickcompleteInputParam body = new MeEleRetailOrderPickcompleteInputParam();
        body.setOrder_id(orderId);
        param.setBody(body);

        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderPickcompleteResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderPickcompleteResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                Object data = commonBusinessCatResult.getData();

                return new ResultBean().success(data);
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }


    @RequestMapping("selfDeliveryLocationSync")
    public ResultBean selfDeliveryLocationSync() {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderSelfDeliveryLocationSyncParam param = new OrderSelfDeliveryLocationSyncParam();
        //业务请求参数
        MeEleNopDoaApiParamRequestOrderOrderSelfLocationReqDto body = new MeEleNopDoaApiParamRequestOrderOrderSelfLocationReqDto();
        body.setOrder_id("4081696010140097194");
        MeEleNopDoaApiParamRequestOrderLocationReqDto meEleNopDoaApiParamRequestOrderLocationReqDto = new MeEleNopDoaApiParamRequestOrderLocationReqDto();
        meEleNopDoaApiParamRequestOrderLocationReqDto.setUTC("1608627034");
        meEleNopDoaApiParamRequestOrderLocationReqDto.setAltitude("20");
        meEleNopDoaApiParamRequestOrderLocationReqDto.setLatitude("40.04");
        meEleNopDoaApiParamRequestOrderLocationReqDto.setLongitude("116.31");

        body.setLocation(meEleNopDoaApiParamRequestOrderLocationReqDto);
        param.setBody(body);

        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderSelfDeliveryLocationSyncResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderSelfDeliveryLocationSyncResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                Object data = commonBusinessCatResult.getData();

                return new ResultBean().success(data);
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }


    @RequestMapping("getOrderDetail")
    public ResultBean getOrderDetail() {

        //传入APP ID和 APP secret
        // 初始化引擎类
        ApiExecutor apiExecutor = new ApiExecutor(appkey, secKey);
        OrderGetParam param = new OrderGetParam();
        //业务请求参数
        MeEleRetailOrderGetReqDto body = new MeEleRetailOrderGetReqDto();
        body.setOrder_id("4081696010140097194");
        param.setBody(body);

        //请求随机ticket
        param.setTicket(UUID.randomUUID().toString().toUpperCase());

        try {
            // 此处的CommonBusinessCatResult需要替换相应的XXXXResult
            BizResultWrapper<OrderGetResult> result = apiExecutor.send(param);
            logger.error("Result:" + JSON.toJSONString(result));
            if (null == result || null == result.getBody()) {
                logger.error("返回数据中对象为空");
                return new ResultBean().error("返回数据中对象为空");
            }
            OrderGetResult commonBusinessCatResult = result.getBody();
            if (null != commonBusinessCatResult.getErrno() && SUCCESS_FLAG.equals(commonBusinessCatResult.getErrno().toString())) {
                MeEleNopDoaApiDtoOrderGetOrderGetDataResultDataDto data = commonBusinessCatResult.getData();
                logger.error(JSON.toJSONString(data));
                return new ResultBean().success(data);
            } else {
                return new ResultBean().error(commonBusinessCatResult.getError());
            }
        } catch (Exception e) {
            logger.error("请求失败，请求异常");
            logger.error(e);
        }
        return new ResultBean().success();
    }

    /**
     * 根据deliveryfee配置和数量计算配送费
     * @param deliveryFeeJson deliveryfee字段的JSON字符串，格式：[{"max":1,"fee":"9.5"},{"max":9,"fee":"9"},{"max":null,"fee":"8"}]
     * @param quantity 商品数量
     * @return 配送费
     */
    private BigDecimal calculateDeliveryFee(String deliveryFeeJson, Integer quantity) {
        if (org.apache.commons.lang3.StringUtils.isBlank(deliveryFeeJson) || quantity == null || quantity <= 0) {
            return BigDecimal.ZERO;
        }

        try {
            com.alibaba.fastjson.JSONArray deliveryFeeArray = JSON.parseArray(deliveryFeeJson);
            if (deliveryFeeArray == null || deliveryFeeArray.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 遍历配送费配置，找到匹配的区间
            for (Object item : deliveryFeeArray) {
                JSONObject feeConfig = (JSONObject) item;
                Object maxObj = feeConfig.get("max");
                String feeStr = feeConfig.getString("fee");

                if (org.apache.commons.lang3.StringUtils.isBlank(feeStr)) {
                    continue;
                }

                // 如果max为null，表示无上限，直接返回该费率
                if (maxObj == null) {
                    return new BigDecimal(feeStr);
                }

                // 如果数量小于等于max值，返回该费率
                Integer maxValue = Integer.valueOf(maxObj.toString());
                if (quantity <= maxValue) {
                    return new BigDecimal(feeStr);
                }
            }

            // 如果没有找到匹配的区间，返回0
            return BigDecimal.ZERO;

        } catch (Exception e) {
            logger.error("解析配送费配置失败，deliveryFeeJson: " + deliveryFeeJson + ", quantity: " + quantity, e);
            return BigDecimal.ZERO;
        }
    }

}