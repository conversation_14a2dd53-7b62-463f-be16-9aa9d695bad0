<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.waterstationbuyproducer.dao.OrderSourceConnectMapper">
  
  <resultMap id="BaseResultMap" type="com.example.waterstationbuyproducer.entity.OrderSourceConnect">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="union_code" jdbcType="VARCHAR" property="unionCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="product_new_id" jdbcType="BIGINT" property="productNewId" />
    <result column="order_source_id" jdbcType="BIGINT" property="orderSourceId" />
    <result column="product_quantity" jdbcType="INTEGER" property="productQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, union_code, name, product_new_id, order_source_id, product_quantity, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
  </select>

  <select id="selectByOrderSourceIdAndUnionCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
    where order_source_id = #{orderSourceId,jdbcType=BIGINT} 
    and union_code = #{unionCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByOrderSourceId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
    where order_source_id = #{orderSourceId,jdbcType=BIGINT}
  </select>

  <select id="selectByProductNewId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
    where product_new_id = #{productNewId,jdbcType=BIGINT}
  </select>

  <select id="selectByUnionCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_source_connect
    where union_code = #{unionCode,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_source_connect
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.example.waterstationbuyproducer.entity.OrderSourceConnect">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_source_connect (union_code, name, product_new_id,
      order_source_id, product_quantity, create_time, update_time)
    values (#{unionCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{productNewId,jdbcType=BIGINT},
      #{orderSourceId,jdbcType=BIGINT}, #{productQuantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.example.waterstationbuyproducer.entity.OrderSourceConnect">
    update order_source_connect
    set union_code = #{unionCode,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      product_new_id = #{productNewId,jdbcType=BIGINT},
      order_source_id = #{orderSourceId,jdbcType=BIGINT},
      product_quantity = #{productQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 分页查询关联关系（支持搜索条件） -->
  <select id="selectByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_source_connect
    <where>
      <if test="unionCode != null and unionCode != ''">
        and union_code like concat('%', #{unionCode}, '%')
      </if>
      <if test="name != null and name != ''">
        and (
        <foreach collection="name.trim().split('\\s+')" item="keyword" separator=" and " open="" close="">
          `name` like concat('%', #{keyword}, '%')
        </foreach>
        )
      </if>
      <if test="orderSourceId != null">
        and order_source_id = #{orderSourceId}
      </if>
      <if test="configStatus != null and configStatus != ''">
        <if test="configStatus == 'configured'">
          and product_new_id is not null
        </if>
        <if test="configStatus == 'unconfigured'">
          and product_new_id is null
        </if>
      </if>
    </where>
    order by create_time desc
    limit #{offset}, #{pageSize}
  </select>

  <!-- 统计查询结果总数（支持搜索条件） -->
  <select id="countByConditions" resultType="java.lang.Long">
    select count(*)
    from order_source_connect
    <where>
      <if test="unionCode != null and unionCode != ''">
        and union_code like concat('%', #{unionCode}, '%')
      </if>
      <if test="name != null and name != ''">
        and (
        <foreach collection="name.trim().split('\\s+')" item="keyword" separator=" or " open="" close="">
          name like concat('%', #{keyword}, '%')
        </foreach>
        )
      </if>
      <if test="orderSourceId != null">
        and order_source_id = #{orderSourceId}
      </if>
      <if test="configStatus != null and configStatus != ''">
        <if test="configStatus == 'configured'">
          and product_new_id is not null
        </if>
        <if test="configStatus == 'unconfigured'">
          and product_new_id is null
        </if>
      </if>
    </where>
  </select>
</mapper>
